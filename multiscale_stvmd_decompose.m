% =========================================================================
% multiscale_stvmd_decompose.m
% 多尺度短时变分模态分解函数，实现对复杂非平稳信号的时频局部化分解
% =========================================================================
function [u_k_multiscale, omega_k_multiscale] = multiscale_stvmd_decompose(signal, params)
    % 初始化参数
    K = params.K_vmd;
    alpha = params.vmd_alpha;
    tau = params.vmd_tau;
    tol = params.vmd_tol;
    max_iter = params.max_iter_vmd;
    window_sizes = params.window_sizes;
    overlap = params.overlap;
    
    % 信号长度
    signal_length = length(signal);
    
    % 初始化多尺度结果容器
    u_k_multiscale = cell(length(window_sizes), 1);
    omega_k_multiscale = cell(length(window_sizes), 1);
    
    % 对每个尺度执行STVMD分解
    for scale_idx = 1:length(window_sizes)
        win_size = window_sizes(scale_idx);
        
        % 确保窗口大小不超过信号长度
        if win_size > signal_length
            win_size = signal_length;
        end
        
        % 计算步长
        step = floor(win_size * (1 - overlap));
        num_segments = floor((signal_length - win_size) / step) + 1;
        
        % 初始化分段结果
        u_segments = zeros(num_segments, win_size, K, 'like', 1j*signal(1));
        omega_segments = zeros(K, num_segments);
        
        % 应用窗函数
        window_func = hamming(win_size)';
        
        % 对每个分段执行VMD
        for seg_idx = 1:num_segments
            % 提取当前段
            start_idx = (seg_idx-1) * step + 1;
            end_idx = min(start_idx + win_size - 1, signal_length);
            
            % 处理边界情况
            if end_idx - start_idx + 1 < win_size
                segment = zeros(1, win_size, 'like', signal);
                segment(1:(end_idx-start_idx+1)) = signal(start_idx:end_idx);
            else
                segment = signal(start_idx:end_idx);
            end
            
            % 应用窗函数
            windowed_segment = segment .* window_func;
            
            % 执行VMD分解
            [u_k, omega_k] = vmd_core(windowed_segment, K, alpha, tau, tol, max_iter);
            
            % 存储结果
            u_segments(seg_idx, 1:length(segment), :) = reshape(u_k, [1, length(segment), K]);
            omega_segments(:, seg_idx) = omega_k;
        end
        
        % 重叠相加合成完整长度的模态函数
        u_k_full = zeros(K, signal_length, 'like', 1j*signal(1));
        normalization = zeros(1, signal_length);
        
        for seg_idx = 1:num_segments
            start_idx = (seg_idx-1) * step + 1;
            end_idx = min(start_idx + win_size - 1, signal_length);
            segment_length = end_idx - start_idx + 1;
            
            for k = 1:K
                % 重叠相加
                u_k_full(k, start_idx:end_idx) = u_k_full(k, start_idx:end_idx) + ...
                    reshape(u_segments(seg_idx, 1:segment_length, k), [1, segment_length]) .* window_func(1:segment_length);
                
                % 累积窗函数权重
                if k == 1
                    normalization(start_idx:end_idx) = normalization(start_idx:end_idx) + window_func(1:segment_length);
                end
            end
        end
        
        % 归一化
        normalization(normalization < eps) = 1;
        for k = 1:K
            u_k_full(k, :) = u_k_full(k, :) ./ normalization;
        end
        
        % 计算平均中心频率
        omega_k_avg = mean(omega_segments, 2);
        
        % 存储当前尺度的结果
        u_k_multiscale{scale_idx} = u_k_full;
        omega_k_multiscale{scale_idx} = omega_k_avg;
    end
end

% VMD核心算法
function [u, omega_k, u_hat] = vmd_core(signal, K, alpha, tau, tol, max_iter)
    % 初始化
    signal = signal(:).'; % 确保为行向量
    N = length(signal);
    
    % 频域相关
    signal_hat = fft(signal);
    freqs = (0:N-1) / N;
    freqs(freqs > 0.5) = freqs(freqs > 0.5) - 1; % 将频率映射到[-0.5, 0.5]
    
    % 初始化模态和中心频率
    u_hat = zeros(K, N, 'like', 1j*signal(1));
    omega_k = zeros(K, 1);
    
    % 均匀初始化中心频率
    for k = 1:K
        omega_k(k) = (k-1) / K;
    end
    
    % 初始化拉格朗日乘子
    lambda_hat = zeros(1, N, 'like', 1j*signal(1));
    
    % 动态调整alpha
    alpha_adjusted = alpha * ones(1, K);
    
    % 主迭代循环
    for iter = 1:max_iter
        % 保存前一轮迭代结果用于收敛检查
        u_hat_prev = u_hat;
        omega_prev = omega_k;
        
        % 更新每个模态
        for k = 1:K
            % 计算其他模态之和
            sum_uk = sum(u_hat, 1) - u_hat(k, :);
            
            % 更新u_hat_k
            num = signal_hat - sum_uk + lambda_hat/2;
            den = 1 + 2*alpha_adjusted(k)*(freqs - omega_k(k)).^2;
            u_hat(k, :) = num ./ den;
            
            % 更新omega_k
            power_spectrum = abs(u_hat(k, :)).^2;
            if sum(power_spectrum) > eps
                omega_k(k) = sum(freqs .* power_spectrum) / sum(power_spectrum);
            end
        end
        
        % 更新拉格朗日乘子
        if tau > 0
            lambda_hat = lambda_hat + tau * (sum(u_hat, 1) - signal_hat);
        end
        
        % 收敛检查
        converged = true;
        for k = 1:K
            if norm(u_hat(k, :) - u_hat_prev(k, :)) / norm(u_hat_prev(k, :) + eps) > tol
                converged = false;
                break;
            end
            
            if abs(omega_k(k) - omega_prev(k)) > tol
                converged = false;
                break;
            end
        end
        
        if converged
            break;
        end
    end
    
    % 转换回时域
    u = zeros(K, N, 'like', 1j*signal(1));
    for k = 1:K
        u(k, :) = real(ifft(u_hat(k, :)));
    end
end 