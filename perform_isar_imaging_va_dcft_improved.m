% =========================================================================
% perform_isar_imaging_va_dcft_improved.m
% 增强版VMD-ADMM-OptimizedDCFT ISAR成像算法
% 增加了直流分量去除、窗函数处理、零多普勒抑制等功能
% =========================================================================
function [ISAR_image_sparse_shifted, s_compensated_dominant_mode] = perform_isar_imaging_va_dcft_improved(radar_data, params_proc)
    [num_range_bins, num_azimuth] = size(radar_data);
    fprintf('  处理数据尺寸: %d x %d\n', num_range_bins, num_azimuth);

    K_vmd = params_proc.vmd.K;
    % 归一化慢时间轴 (0 到 N-1)/N，用于VMD和相位估计内部计算
    tm_normalized = (0:num_azimuth-1) / num_azimuth; 

    ISAR_image_sparse_unsh = zeros(num_range_bins, num_azimuth, 'like', 1j*radar_data(1));
    s_compensated_dominant_mode = zeros(size(radar_data), 'like', radar_data);
    
    % 生成窗函数
    window_func = generate_window(num_azimuth, params_proc.window.type);
    
    fprintf('  开始逐距离单元处理 (共 %d 个)...\n', num_range_bins);
    
    % 使用 parfor 进行并行处理 (如果安装了 Parallel Computing Toolbox)
    parfor r_idx = 1:num_range_bins 
    % for r_idx = 1:num_range_bins  % 如果没有并行工具箱，使用这行
        if mod(r_idx, round(num_range_bins/20)) == 0 && r_idx > 1 % 减少打印频率
            fprintf('    正在处理距离单元: %d/%d\n', r_idx, num_range_bins);
        end
        
        signal_orig = radar_data(r_idx, :); 
        
        if sum(abs(signal_orig).^2) < 1e-10 % 能量阈值，避免处理纯噪声
            ISAR_image_sparse_unsh(r_idx, :) = fft(signal_orig); % 或直接置零
            s_compensated_dominant_mode(r_idx, :) = signal_orig;
            continue;
        end
        
        % 去除直流分量 (可选)
        if params_proc.dc_removal
            signal_orig = signal_orig - mean(signal_orig);
        end
        
        % 应用窗函数 (可选)
        signal_windowed = signal_orig .* window_func;
        
        signal_norm_factor = max(abs(signal_windowed));
        if signal_norm_factor == 0, signal_norm_factor = 1; end % 避免除零
        signal = signal_windowed / signal_norm_factor; % 对当前信号进行归一化

        u_k = zeros(K_vmd, num_azimuth, 'like', 1j*signal(1));
        omega_k_vmd = zeros(K_vmd, 1); 
        
        % 初始化频率中心
        if strcmp(params_proc.vmd.init_omega_method, 'peaks')
            [fft_peaks, locs] = findpeaks(abs(fft(signal)), 'SortStr', 'descend', 'NPeaks', K_vmd);
            if ~isempty(locs)
                omega_k_vmd(1:length(locs)) = (locs-1)/num_azimuth; % 归一化频率
            end
            if length(locs) < K_vmd % 如果找到的峰值少于K个
                remaining_indices = (length(locs)+1):K_vmd;
                % 为剩余模态均匀或随机初始化频率
                omega_k_vmd(remaining_indices) = linspace(0.1, 0.4, length(remaining_indices))'; 
            end
        else % linear
             for k_idx_init = 1:K_vmd
                omega_k_vmd(k_idx_init) = (k_idx_init-1)/(K_vmd); 
             end
        end
        
        poly_coeffs_k = zeros(K_vmd, params_proc.phase_est.poly_order); 
        phi_k_phase_model = zeros(K_vmd, num_azimuth, 'like', 1j*signal(1));

        % ADMM变量初始化 (针对当前距离单元)
        X_admm_init = fft(signal); % 初始稀疏重建结果 (频谱)
        Z_admm_init = X_admm_init;      
        Lambda_admm_init = zeros(size(X_admm_init), 'like', 1j*signal(1)); 

        for global_iter = 1:params_proc.fusion.max_global_iter
            signal_sum_prev_iter_u = sum(u_k, 1); 
            
            % VMD引导的信号分解
            [u_k, omega_k_vmd, ~] = vmd_decompose_improved(signal, params_proc.vmd, u_k, omega_k_vmd, phi_k_phase_model, params_proc.fusion.alpha_vmd_guidance);
            
            % 优化后的相位参数估计 (逐模态)
            for k_idx = 1:K_vmd
                if sum(abs(u_k(k_idx,:))) < 1e-7 % 跳过能量极低的模态
                    poly_coeffs_k(k_idx,:) = 0;
                    phi_k_phase_model(k_idx,:) = 0;
                    continue;
                end
                [estimated_coeffs, estimated_phase] = estimate_phase_polynomial_improved(u_k(k_idx,:), ...
                                                                  poly_coeffs_k(k_idx,:), ... % 使用上一轮结果作为初始值
                                                                  params_proc.phase_est, ...
                                                                  tm_normalized, params_proc.PRF);
                poly_coeffs_k(k_idx,:) = estimated_coeffs;
                phi_k_phase_model(k_idx,:) = estimated_phase;
            end
            
            if global_iter > 1
                change = norm(sum(u_k, 1) - signal_sum_prev_iter_u) / (norm(signal_sum_prev_iter_u) + eps);
                if change < params_proc.fusion.global_tol
                    break;
                end
            end
        end 
        
        Y_compensated_sum = zeros(1, num_azimuth, 'like', 1j*signal(1));
        for k_idx = 1:K_vmd
             % 仅使用具有显著能量的模态进行叠加
            if sum(abs(u_k(k_idx,:))) > 1e-6 
                Y_compensated_sum = Y_compensated_sum + u_k(k_idx,:) .* exp(-1j * phi_k_phase_model(k_idx,:));
            end
        end
        
        if sum(abs(Y_compensated_sum)) < 1e-9 % 如果补偿后信号能量过低
            X_reconstructed_spectrum = fft(signal); % Fallback to simple FFT
        else
            Y_fft = fft(Y_compensated_sum);
            
            % 应用零多普勒抑制 (可选)
            if params_proc.zero_doppler_suppress
                Y_fft = apply_zero_doppler_suppression(Y_fft, params_proc.zero_doppler_width);
            end
            
            [X_reconstructed_spectrum, ~, ~] = admm_reconstruct_improved(Y_fft, params_proc.admm, X_admm_init, Z_admm_init, Lambda_admm_init);
        end
        
        ISAR_image_sparse_unsh(r_idx, :) = X_reconstructed_spectrum * signal_norm_factor; 
        
        % 计算主导模态及其相位补偿
        mode_energies = sum(abs(u_k).^2, 2);
        [~, dominant_idx] = max(mode_energies);
        if ~isempty(dominant_idx) && dominant_idx <= size(phi_k_phase_model,1) % 确保索引有效
            dominant_phase_compensation = phi_k_phase_model(dominant_idx(1), :); % dominant_idx可能返回多个最大值
            s_compensated_dominant_mode(r_idx, :) = signal_orig .* exp(-1j * dominant_phase_compensation);
        else
            s_compensated_dominant_mode(r_idx, :) = signal_orig; % 无有效主导模态
        end
    end 
    fprintf('  所有距离单元处理完毕。\n');
    
    % 零多普勒抑制 (全局级别)
    if params_proc.zero_doppler_suppress
        ISAR_image_sparse_unsh = apply_zero_doppler_suppression_global(ISAR_image_sparse_unsh, params_proc.zero_doppler_width);
    end
    
    ISAR_image_sparse_shifted = fftshift(ISAR_image_sparse_unsh, 2); % 在函数末尾进行一次fftshift
end

% 生成窗函数
function window = generate_window(length, type)
    switch lower(type)
        case 'hamming'
            window = hamming(length)';
        case 'hanning'
            window = hanning(length)';
        case 'blackman'
            window = blackman(length)';
        case 'none'
            window = ones(1, length);
        otherwise
            window = hamming(length)';
    end
end

% 零多普勒抑制函数
function signal_fft = apply_zero_doppler_suppression(signal_fft, half_width)
    N = length(signal_fft);
    center_idx = 1; % FFT中的DC分量索引
    
    % 使用高斯加权衰减而不是硬截断
    for offset = -half_width:half_width
        idx = mod(center_idx + offset - 1, N) + 1;
        weight = (abs(offset)/half_width)^2; % 二次加权
        signal_fft(idx) = signal_fft(idx) * weight;
    end
end

% 全局零多普勒抑制 (对整个图像)
function image_data = apply_zero_doppler_suppression_global(image_data, half_width)
    [num_rows, num_cols] = size(image_data);
    center_idx = 1; % FFT中的DC分量索引
    
    % 对所有距离单元执行相同的零多普勒抑制
    for offset = -half_width:half_width
        idx = mod(center_idx + offset - 1, num_cols) + 1;
        weight = (abs(offset)/half_width)^2; % 二次加权
        image_data(:, idx) = image_data(:, idx) * weight;
    end
end 