% =========================================================================
% estimate_phase_polynomial_improved.m
% 改进的相位多项式估计，支持四阶相位多项式，提高复杂运动下的相位估计精度
% =========================================================================
function [poly_coeffs_estimated, phase_estimated] = estimate_phase_polynomial_improved(signal_mode, initial_coeffs, params_phase_est, tm_normalized, PRF)
    N = length(signal_mode);
    poly_order = params_phase_est.poly_order;
    poly_coeffs_estimated = zeros(1, poly_order);

    % --- 1. 估计 fd (线性项系数, 归一化多普勒中心) ---
    signal_mode_fft = fft(signal_mode);
    abs_fft = abs(signal_mode_fft);
    
    % 使用更稳健的方法找出主峰值
    % 平滑频谱，减少噪声对估计的影响
    abs_fft_smooth = smooth_spectrum(abs_fft, 5);
    [~, idx_max_fft] = max(abs_fft_smooth);
    
    fd_est_norm = (idx_max_fft - 1) / N; 
    if fd_est_norm > 0.5 
        fd_est_norm = fd_est_norm - 1; % 调整到 [-0.5, 0.5]
    end
    poly_coeffs_estimated(1) = fd_est_norm;

    % --- 2. 使用迭代改进方法估计相位系数 ---
    % 初始化：补偿线性相位
    signal_comp = signal_mode .* exp(-1j * 2*pi * poly_coeffs_estimated(1) * tm_normalized);
    
    % 二次项系数估计
    if poly_order >= 2
        % 使用细化搜索算法
        ka_norm_max_abs = 2.0 * params_phase_est.fd_search_range_factor;
        ka_search_center = initial_coeffs(2); % 使用上一轮或初始猜测作为中心
        
        % 粗略搜索
        ka_search_values_coarse = linspace(ka_search_center - ka_norm_max_abs, 
                                         ka_search_center + ka_norm_max_abs, 
                                         params_phase_est.ka_search_pts);
        
        sharpness_ka_coarse = evaluate_parameter_sharpness(signal_comp, ka_search_values_coarse, tm_normalized, 2);
        [~, idx_max_ka_coarse] = max(sharpness_ka_coarse);
        
        if ~isempty(idx_max_ka_coarse)
            ka_coarse_estimate = ka_search_values_coarse(idx_max_ka_coarse(1));
            
            % 细化搜索
            ka_fine_range = ka_norm_max_abs / 5; % 缩小搜索范围
            ka_search_values_fine = linspace(ka_coarse_estimate - ka_fine_range, 
                                           ka_coarse_estimate + ka_fine_range, 
                                           params_phase_est.ka_search_pts);
                                           
            sharpness_ka_fine = evaluate_parameter_sharpness(signal_comp, ka_search_values_fine, tm_normalized, 2);
            [~, idx_max_ka_fine] = max(sharpness_ka_fine);
            
            if ~isempty(idx_max_ka_fine)
                poly_coeffs_estimated(2) = ka_search_values_fine(idx_max_ka_fine(1));
            else
                poly_coeffs_estimated(2) = ka_coarse_estimate;
            end
        else
            poly_coeffs_estimated(2) = initial_coeffs(2); % 使用初始值
        end
    end
    
    % 补偿二次相位
    if poly_order >= 2
        signal_comp = signal_comp .* exp(-1j * pi * poly_coeffs_estimated(2) * tm_normalized.^2);
    end

    % 三次项系数估计
    if poly_order >= 3
        kb_norm_max_abs = 2.0 * params_phase_est.fd_search_range_factor * 2; // 三阶项搜索范围更大
        kb_search_center = initial_coeffs(3);
        
        % 粗略搜索
        kb_search_values_coarse = linspace(kb_search_center - kb_norm_max_abs, 
                                         kb_search_center + kb_norm_max_abs, 
                                         params_phase_est.kb_search_pts);
                                         
        sharpness_kb_coarse = evaluate_parameter_sharpness(signal_comp, kb_search_values_coarse, tm_normalized, 3);
        [~, idx_max_kb_coarse] = max(sharpness_kb_coarse);
        
        if ~isempty(idx_max_kb_coarse)
            kb_coarse_estimate = kb_search_values_coarse(idx_max_kb_coarse(1));
            
            % 细化搜索
            kb_fine_range = kb_norm_max_abs / 5;
            kb_search_values_fine = linspace(kb_coarse_estimate - kb_fine_range, 
                                           kb_coarse_estimate + kb_fine_range, 
                                           params_phase_est.kb_search_pts);
                                           
            sharpness_kb_fine = evaluate_parameter_sharpness(signal_comp, kb_search_values_fine, tm_normalized, 3);
            [~, idx_max_kb_fine] = max(sharpness_kb_fine);
            
            if ~isempty(idx_max_kb_fine)
                poly_coeffs_estimated(3) = kb_search_values_fine(idx_max_kb_fine(1));
            else
                poly_coeffs_estimated(3) = kb_coarse_estimate;
            end
        else
            poly_coeffs_estimated(3) = initial_coeffs(3);
        end
    end
    
    % 补偿三次相位
    if poly_order >= 3
        signal_comp = signal_comp .* exp(-1j * (pi/3) * poly_coeffs_estimated(3) * tm_normalized.^3);
    end
    
    % 四次项系数估计 (如果需要)
    if poly_order >= 4
        kc_norm_max_abs = 4.0 * params_phase_est.fd_search_range_factor;
        kc_search_center = 0; % 默认中心为0
        if length(initial_coeffs) >= 4
            kc_search_center = initial_coeffs(4);
        end
        
        kc_search_values = linspace(kc_search_center - kc_norm_max_abs, 
                                   kc_search_center + kc_norm_max_abs, 
                                   params_phase_est.kc_search_pts);
                                   
        sharpness_kc = evaluate_parameter_sharpness(signal_comp, kc_search_values, tm_normalized, 4);
        [~, idx_max_kc] = max(sharpness_kc);
        
        if ~isempty(idx_max_kc)
            poly_coeffs_estimated(4) = kc_search_values(idx_max_kc(1));
        else
            poly_coeffs_estimated(4) = kc_search_center;
        end
    end

    % --- 构建最终的估计相位 (基于归一化时间和归一化系数) ---
    phase_estimated = zeros(size(tm_normalized), 'like', 1j); % 确保复数类型
    
    % 构建完整的相位多项式
    for order = 1:poly_order
        switch order
            case 1 % 线性项
                phase_estimated = phase_estimated + 2*pi * poly_coeffs_estimated(1) * tm_normalized;
            case 2 % 二次项
                phase_estimated = phase_estimated + 2*pi * 0.5 * poly_coeffs_estimated(2) * tm_normalized.^2;
            case 3 % 三次项
                phase_estimated = phase_estimated + 2*pi * (1/6) * poly_coeffs_estimated(3) * tm_normalized.^3;
            case 4 % 四次项
                phase_estimated = phase_estimated + 2*pi * (1/24) * poly_coeffs_estimated(4) * tm_normalized.^4;
        end
    end
end

% 评估特定参数对应的信号锐度
function sharpness = evaluate_parameter_sharpness(signal, param_values, tm_normalized, order)
    N = length(param_values);
    sharpness = zeros(1, N);
    
    for i = 1:N
        param = param_values(i);
        switch order
            case 2 % 二次项
                dechirped_signal = signal .* exp(-1j * pi * param * tm_normalized.^2);
            case 3 % 三次项
                dechirped_signal = signal .* exp(-1j * (pi/3) * param * tm_normalized.^3);
            case 4 % 四次项
                dechirped_signal = signal .* exp(-1j * (pi/12) * param * tm_normalized.^4);
        end
        
        % 计算锐度 - 使用几种不同的指标
        spectrum = abs(fft(dechirped_signal)).^2;
        
        % 1. L4范数 (标准锐度指标)
        sharpness_l4 = sum(spectrum.^2);
        
        % 2. 最大值与均值的比值 (对比度)
        sharpness_contrast = max(spectrum) / (mean(spectrum) + eps);
        
        % 3. 负熵 (更集中的分布具有更低的熵)
        p = spectrum / (sum(spectrum) + eps);
        p_valid = p(p > eps);
        sharpness_entropy = -sum(p_valid .* log2(p_valid));
        sharpness_entropy = -sharpness_entropy; % 负熵，使更集中的分布具有更高的值
        
        % 综合多种指标
        sharpness(i) = sharpness_l4 + 0.2*sharpness_contrast + 0.1*sharpness_entropy;
    end
end

% 平滑频谱函数
function spectrum_smooth = smooth_spectrum(spectrum, window_size)
    N = length(spectrum);
    spectrum_smooth = spectrum;
    half_window = floor(window_size/2);
    
    for i = 1:N
        window_start = max(1, i-half_window);
        window_end = min(N, i+half_window);
        spectrum_smooth(i) = mean(spectrum(window_start:window_end));
    end
end 