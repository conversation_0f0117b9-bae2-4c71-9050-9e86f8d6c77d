% =========================================================================
% run_st_dcft_admm_isar.m
% STVMD-DCFT-ADMM融合ISAR成像算法主程序
% =========================================================================
clear; close all; clc;

% 加载回波数据
fprintf('正在加载雷达数据...\n');
try
    load shipx2.mat
    echo_data = shipx2;
    fprintf('成功加载shipx2.mat数据\n');
catch
    fprintf('无法加载shipx2.mat，尝试生成模拟回波数据...\n');
    [echo_data, sim_params] = generate_simulated_echo();
end

% 设置算法参数
params = struct();

% STVMD参数
params.K_vmd = 5;                % 模态数量
params.vmd_alpha = 2000;         % 带宽约束强度
params.vmd_tau = 0.1;            % 拉格朗日乘子更新率
params.vmd_tol = 1e-7;           % 收敛容限
params.max_iter_vmd = 300;       % 最大迭代次数
params.window_sizes = [16, 32, 64]; % 多尺度窗口大小
params.overlap = 0.5;            % 窗口重叠率

% DCFT参数
params.phase_model_order = 4;    % 相位模型阶数
params.dcft_alpha_range = [-80, 80]; % 二次相位系数搜索范围
params.dcft_alpha_step = 2;      % 二次相位系数搜索步长
params.dcft_beta_range = [-400, 400]; % 三次相位系数搜索范围
params.dcft_beta_step = 50;      % 三次相位系数搜索步长

% ADMM参数
params.admm_rho = 1.5;           % 增广拉格朗日参数
params.admm_lambda = 0.008;      % 稀疏正则化权重
params.admm_max_iter = 200;      % 最大迭代次数
params.admm_tol = 1e-6;          % 收敛容限

% 其他参数
params.window_type = 'hamming';  % 窗函数类型
params.dc_suppress = true;       % 是否抑制直流分量
params.dc_width = 5;             % 直流抑制窗口半宽度
params.global_iterations = 3;    % 全局迭代次数
params.auto_focus = true;        % 是否使用自动聚焦
params.display_progress = true;  % 是否显示处理进度

% 雷达系统参数
params.PRF = 1400;               % 脉冲重复频率
params.fc = 5.2e9;               % 载频
params.c = 3e8;                  % 光速

% 执行STVMD-DCFT-ADMM融合算法
fprintf('开始执行STVMD-DCFT-ADMM融合ISAR成像算法...\n');
tic;
[ISAR_image_enhanced, s_compensated] = ST_DCFT_ADMM_ISAR(echo_data, params);
processing_time = toc;
fprintf('处理完成。耗时: %.2f 秒\n', processing_time);

% 对比：直接FFT成像结果
ISAR_image_fft = fftshift(fft(echo_data, [], 2), 2);

% 显示原始FFT成像结果
figure('Name', '原始回波直接FFT成像');
imagesc(20*log10(abs(ISAR_image_fft)./max(abs(ISAR_image_fft(:)))));
xlabel('方位单元 (Doppler Frequency)');
ylabel('距离单元 (Range)');
title('原始回波直接FFT成像结果 (dB)');
colorbar;
caxis([-40, 0]); % 设置dB显示范围
axis xy;
colormap(jet);

% 显示STVMD-DCFT-ADMM融合算法成像结果
figure('Name', 'STVMD-DCFT-ADMM融合ISAR成像');
imagesc(20*log10(abs(ISAR_image_enhanced)./max(abs(ISAR_image_enhanced(:)))));
xlabel('方位单元 (Doppler Frequency)');
ylabel('距离单元 (Range)');
title('STVMD-DCFT-ADMM融合ISAR成像结果 (dB)');
colorbar;
caxis([-50, 0]); % 设置dB显示范围
axis xy;
colormap(jet);

% 计算图像质量指标
contrast_fft = calculate_contrast(abs(ISAR_image_fft));
entropy_fft = calculate_entropy(abs(ISAR_image_fft));
contrast_enhanced = calculate_contrast(abs(ISAR_image_enhanced));
entropy_enhanced = calculate_entropy(abs(ISAR_image_enhanced));

fprintf('\n图像质量评估:\n');
fprintf('原始FFT成像 - 对比度: %.4f, 熵: %.4f\n', contrast_fft, entropy_fft);
fprintf('融合算法成像 - 对比度: %.4f, 熵: %.4f\n', contrast_enhanced, entropy_enhanced);
fprintf('对比度提升: %.2f%%, 熵降低: %.2f%%\n', 
       (contrast_enhanced-contrast_fft)/contrast_fft*100,
       (entropy_fft-entropy_enhanced)/entropy_fft*100);

% 辅助函数: 图像对比度计算
function contrast = calculate_contrast(image_data)
    mean_val = mean(image_data(:));
    std_val = std(image_data(:));
    if mean_val > 0
        contrast = std_val / mean_val;
    else
        contrast = 0;
    end
end

% 辅助函数: 图像熵计算
function entropy = calculate_entropy(image_data)
    normalized_data = image_data / sum(image_data(:) + eps);
    valid_data = normalized_data(normalized_data > eps);
    entropy = -sum(valid_data .* log2(valid_data));
end 