% =========================================================================
% ST_DCFT_ADMM_ISAR.m
% 融合短时变分模态分解(STVMD)、离散啁啾傅里叶变换(DCFT)和ADMM的ISAR成像算法
% 专为三维复杂运动目标设计，解决散焦和零多普勒竖线问题
% =========================================================================
function [ISAR_image_enhanced, s_compensated] = ST_DCFT_ADMM_ISAR(radar_data, params)
    % 参数初始化和设置默认值
    if ~isfield(params, 'window_sizes'), params.window_sizes = [16, 32, 64]; end
    if ~isfield(params, 'overlap'), params.overlap = 0.5; end
    if ~isfield(params, 'K_vmd'), params.K_vmd = 4; end
    if ~isfield(params, 'vmd_alpha'), params.vmd_alpha = 2000; end
    if ~isfield(params, 'vmd_tau'), params.vmd_tau = 0.1; end
    if ~isfield(params, 'vmd_tol'), params.vmd_tol = 1e-7; end
    if ~isfield(params, 'max_iter_vmd'), params.max_iter_vmd = 300; end
    if ~isfield(params, 'phase_model_order'), params.phase_model_order = 4; end
    if ~isfield(params, 'dcft_alpha_range'), params.dcft_alpha_range = [-80, 80]; end
    if ~isfield(params, 'dcft_alpha_step'), params.dcft_alpha_step = 2; end
    if ~isfield(params, 'dcft_beta_range'), params.dcft_beta_range = [-400, 400]; end
    if ~isfield(params, 'dcft_beta_step'), params.dcft_beta_step = 50; end
    if ~isfield(params, 'admm_rho'), params.admm_rho = 1.5; end
    if ~isfield(params, 'admm_lambda'), params.admm_lambda = 0.008; end
    if ~isfield(params, 'admm_max_iter'), params.admm_max_iter = 200; end
    if ~isfield(params, 'admm_tol'), params.admm_tol = 1e-6; end
    if ~isfield(params, 'window_type'), params.window_type = 'hamming'; end
    if ~isfield(params, 'dc_suppress'), params.dc_suppress = true; end
    if ~isfield(params, 'dc_width'), params.dc_width = 5; end
    if ~isfield(params, 'global_iterations'), params.global_iterations = 3; end
    if ~isfield(params, 'auto_focus'), params.auto_focus = true; end
    if ~isfield(params, 'display_progress'), params.display_progress = true; end
    
    % 获取数据尺寸
    [num_range_bins, num_azimuth] = size(radar_data);
    
    % 初始化输出变量
    ISAR_image_enhanced = zeros(num_range_bins, num_azimuth, 'like', 1j*radar_data(1));
    s_compensated = zeros(size(radar_data), 'like', radar_data);
    
    % 计算归一化慢时间轴
    tm_normalized = (0:num_azimuth-1) / num_azimuth;
    
    % 创建窗函数
    window_func = generate_window(num_azimuth, params.window_type);
    
    % 应用强直流抑制 (预处理阶段)
    if params.dc_suppress
        radar_data = apply_dc_suppression(radar_data);
    end
    
    if params.display_progress
        fprintf('开始ST-DCFT-ADMM ISAR处理 (共 %d 个距离单元)...\n', num_range_bins);
    end
    
    % 多尺度STVMD-DCFT联合处理
    for global_iter = 1:params.global_iterations
        if params.display_progress
            fprintf('全局迭代: %d/%d\n', global_iter, params.global_iterations);
        end
        
        % 处理每个距离单元
        parfor r_idx = 1:num_range_bins
            if params.display_progress && mod(r_idx, round(num_range_bins/10)) == 0
                fprintf('  处理距离单元: %d/%d\n', r_idx, num_range_bins);
            end
            
            % 获取当前距离单元信号
            signal = radar_data(r_idx, :);
            
            % 跳过能量过低的距离单元
            if sum(abs(signal).^2) < 1e-10
                s_compensated(r_idx, :) = signal;
                continue;
            end
            
            % 应用窗函数
            signal_windowed = signal .* window_func;
            
            % 多尺度短时VMD分解
            [u_k_multiscale, omega_k_multiscale] = multiscale_stvmd_decompose(signal_windowed, params);
            
            % 多尺度DCFT相位估计和补偿
            [phase_error, dominant_modes] = multiscale_dcft_phase_estimation(u_k_multiscale, omega_k_multiscale, params, tm_normalized);
            
            % 相位补偿
            s_compensated(r_idx, :) = signal .* exp(-1j * phase_error);
        end
        
        % 生成当前迭代的ISAR图像
        temp_image = fftshift(fft(s_compensated, [], 2), 2);
        
        % 应用ADMM稀疏重建增强图像质量
        if global_iter == params.global_iterations
            ISAR_image_enhanced = apply_admm_reconstruction(s_compensated, params);
        else
            % 中间迭代使用标准FFT成像
            ISAR_image_enhanced = temp_image;
        end
        
        % 自动聚焦优化 (仅在中间迭代应用)
        if params.auto_focus && global_iter < params.global_iterations
            % 计算图像质量指标
            entropy_before = calculate_image_entropy(abs(ISAR_image_enhanced));
            
            if params.display_progress
                fprintf('  当前图像熵: %.4f\n', entropy_before);
            end
            
            % 应用基于图像质量的相位微调
            s_compensated = autofocus_correction(s_compensated, ISAR_image_enhanced);
            
            % 再次计算图像质量以检查改进
            temp_image = fftshift(fft(s_compensated, [], 2), 2);
            entropy_after = calculate_image_entropy(abs(temp_image));
            
            if params.display_progress
                fprintf('  自动聚焦后图像熵: %.4f (改进: %.4f)\n', 
                       entropy_after, entropy_before - entropy_after);
            end
        end
        
        % 更新雷达数据为补偿后的数据进行下一轮迭代
        radar_data = s_compensated;
    end
    
    % 最终零多普勒抑制 (增强版)
    ISAR_image_enhanced = apply_enhanced_dc_suppression(ISAR_image_enhanced, params.dc_width);
    
    if params.display_progress
        fprintf('ST-DCFT-ADMM ISAR处理完成\n');
    end
end 