% =========================================================================
% multiscale_dcft_phase_estimation.m
% 多尺度DCFT相位估计函数，利用离散啁啾傅里叶变换精确估计复杂运动参数
% =========================================================================
function [phase_error, dominant_modes] = multiscale_dcft_phase_estimation(u_k_multiscale, omega_k_multiscale, params, tm_normalized)
    % 信号长度
    signal_length = length(tm_normalized);
    
    % 初始化相位误差和主导模态
    phase_error = zeros(1, signal_length);
    dominant_modes = cell(length(u_k_multiscale), 1);
    
    % 初始化尺度权重
    scale_weights = zeros(length(u_k_multiscale), 1);
    phase_errors_multiscale = zeros(length(u_k_multiscale), signal_length);
    
    % 设置DCFT参数
    alpha_range = params.dcft_alpha_range;
    alpha_step = params.dcft_alpha_step;
    beta_range = params.dcft_beta_range;
    beta_step = params.dcft_beta_step;
    
    % 设置相位模型阶数
    poly_order = params.phase_model_order;
    
    % 对每个尺度进行处理
    for scale_idx = 1:length(u_k_multiscale)
        u_k = u_k_multiscale{scale_idx};
        omega_k = omega_k_multiscale{scale_idx};
        
        % 计算每个模态的能量
        [K, ~] = size(u_k);
        mode_energies = zeros(K, 1);
        for k = 1:K
            mode_energies(k) = sum(abs(u_k(k, :)).^2);
        end
        
        % 基于能量选择主导模态
        [~, dominant_idx] = max(mode_energies);
        dominant_mode = u_k(dominant_idx, :);
        dominant_modes{scale_idx} = dominant_mode;
        
        % 使用DCFT估计相位参数
        [poly_coeffs, phase_est] = estimate_phase_dcft(dominant_mode, omega_k(dominant_idx), 
                                                      alpha_range, alpha_step, 
                                                      beta_range, beta_step,
                                                      tm_normalized, poly_order);
        
        % 保存该尺度的相位误差
        phase_errors_multiscale(scale_idx, :) = phase_est;
        
        % 计算尺度权重 - 基于相位估计的有效性
        % 使用相位补偿后的频谱集中度作为权重
        compensated_mode = dominant_mode .* exp(-1j * phase_est);
        spectrum = abs(fft(compensated_mode)).^2;
        
        % 使用频谱锐度作为权重
        sharpness = sum(spectrum.^2) / (sum(spectrum)^2 + eps);
        scale_weights(scale_idx) = sharpness * mode_energies(dominant_idx);
    end
    
    % 归一化权重
    if sum(scale_weights) > eps
        scale_weights = scale_weights / sum(scale_weights);
    else
        scale_weights = ones(length(u_k_multiscale), 1) / length(u_k_multiscale);
    end
    
    % 加权融合多尺度相位误差
    for scale_idx = 1:length(u_k_multiscale)
        phase_error = phase_error + scale_weights(scale_idx) * phase_errors_multiscale(scale_idx, :);
    end
end

% DCFT相位估计核心函数
function [poly_coeffs, phase_estimated] = estimate_phase_dcft(signal, center_freq, 
                                                             alpha_range, alpha_step, 
                                                             beta_range, beta_step,
                                                             tm_normalized, poly_order)
    % 信号长度
    N = length(signal);
    
    % 初始化多项式系数
    poly_coeffs = zeros(1, poly_order);
    
    % 首先估计线性项 (fd)
    poly_coeffs(1) = center_freq;
    
    % 去除线性相位项
    signal_detrend = signal .* exp(-1j * 2*pi * poly_coeffs(1) * tm_normalized);
    
    % 定义搜索范围
    alpha_values = alpha_range(1):alpha_step:alpha_range(2);
    beta_values = beta_range(1):beta_step:beta_range(2);
    
    % 初始化最大锐度和最优参数
    max_sharpness = -Inf;
    best_alpha = 0;
    best_beta = 0;
    
    % 网格搜索最优参数
    for alpha_idx = 1:length(alpha_values)
        alpha = alpha_values(alpha_idx);
        
        for beta_idx = 1:length(beta_values)
            beta = beta_values(beta_idx);
            
            % 构建补偿相位
            comp_phase = 2*pi * ((1/2)*alpha*tm_normalized.^2 + (1/6)*beta*tm_normalized.^3);
            
            % 应用相位补偿
            s_comp = signal_detrend .* exp(-1j * comp_phase);
            
            % 计算频谱
            S_comp = fft(s_comp);
            
            % 计算频谱锐度
            spectrum_sq = abs(S_comp).^2;
            sharpness = sum(spectrum_sq.^2) / (sum(spectrum_sq)^2 + eps);
            
            % 更新最优参数
            if sharpness > max_sharpness
                max_sharpness = sharpness;
                best_alpha = alpha;
                best_beta = beta;
            end
        end
    end
    
    % 存储估计的二次和三次项系数
    poly_coeffs(2) = best_alpha;
    poly_coeffs(3) = best_beta;
    
    % 如果需要四阶相位模型，进行四阶项搜索
    if poly_order >= 4
        % 去除已估计的相位
        signal_detrend_higher = signal_detrend .* exp(-1j * 2*pi * ((1/2)*best_alpha*tm_normalized.^2 + 
                                                                    (1/6)*best_beta*tm_normalized.^3));
        
        % 四阶项搜索范围
        gamma_range = [-200, 200];
        gamma_step = 20;
        gamma_values = gamma_range(1):gamma_step:gamma_range(2);
        
        % 搜索最优四阶项
        max_sharpness_gamma = -Inf;
        best_gamma = 0;
        
        for gamma_idx = 1:length(gamma_values)
            gamma = gamma_values(gamma_idx);
            
            % 构建四阶补偿相位
            comp_phase_gamma = 2*pi * (1/24) * gamma * tm_normalized.^4;
            
            % 应用相位补偿
            s_comp_gamma = signal_detrend_higher .* exp(-1j * comp_phase_gamma);
            
            % 计算频谱锐度
            S_comp_gamma = fft(s_comp_gamma);
            spectrum_sq_gamma = abs(S_comp_gamma).^2;
            sharpness_gamma = sum(spectrum_sq_gamma.^2) / (sum(spectrum_sq_gamma)^2 + eps);
            
            % 更新最优参数
            if sharpness_gamma > max_sharpness_gamma
                max_sharpness_gamma = sharpness_gamma;
                best_gamma = gamma;
            end
        end
        
        % 存储四阶系数
        poly_coeffs(4) = best_gamma;
    end
    
    % 构建完整相位估计
    phase_estimated = 2*pi * (poly_coeffs(1) * tm_normalized + 
                             (1/2) * poly_coeffs(2) * tm_normalized.^2 + 
                             (1/6) * poly_coeffs(3) * tm_normalized.^3);
    
    % 添加四阶项（如果需要）
    if poly_order >= 4
        phase_estimated = phase_estimated + 2*pi * (1/24) * poly_coeffs(4) * tm_normalized.^4;
    end
end 