% run_isar_processing.m
% 主脚本，用于运行ISAR回波仿真和VMD-ADMM-DCFT成像处理

clear; close all; clc;
fprintf('开始ISAR成像仿真与处理...\n');

% -------------------- 1. 数据加载 -------------------- %
fprintf('加载雷达回波数据...\n');
tic;
 load shipx2_1000.mat; % 加载实际数据
% echo_data = shipx2; % 确保一致性
load s_r_tm2.mat;
%load mig2555.mat
%load shipx2_3700.mat
% 生成仿真参数（这里保留以便提供必要的处理参数）
[echo_data, sim_params] = generate_simulated_echo();
fprintf('数据加载完毕。耗时: %.2f 秒\n', toc);
fprintf('回波数据尺寸: %d (距离单元) x %d (方位单元)\n', size(echo_data, 1), size(echo_data, 2));

% 确保sim_params与实际数据尺寸匹配
sim_params.Num_r = size(echo_data, 1);
sim_params.Num_tm = size(echo_data, 2);
sim_params.r_axis = linspace(min(sim_params.r_axis), max(sim_params.r_axis), sim_params.Num_r);
sim_params.tm = linspace(0, (sim_params.Num_tm-1)/sim_params.PRF, sim_params.Num_tm);
echo_data=s_r_tm2;
% -------------------- 2. 设置处理参数 -------------------- %
params_proc = struct();

% VMD 参数
params_proc.vmd.K = 3;             % 模态数量 (经验值, 可调整)
params_proc.vmd.alpha = 2000;      % 带宽约束强度
params_proc.vmd.tau = 0.3;         % 拉格朗日乘子更新率 (0表示无噪声)
params_proc.vmd.tol = 1e-2;        % 收敛容限
params_proc.vmd.max_iter = 1;    % VMD最大迭代次数
params_proc.vmd.init_omega_method = 'peaks'; % 'peaks' 或 'linear'

% 相位估算 (优化版 "DCFT") 参数
params_proc.phase_est.poly_order = 3; % 估计的相位多项式阶数 (fd, ka, kb)
params_proc.phase_est.fd_search_range_factor = 0.5; % fd 搜索范围相对于PRF/2
params_proc.phase_est.ka_search_pts = 51;    % ka 搜索点数 (1D搜索)
params_proc.phase_est.kb_search_pts = 51;    % kb 搜索点数 (1D搜索)
% ka, kb 的搜索范围会根据信号特性动态调整一点

% ADMM 参数
params_proc.admm.rho = 1.0;        % 增广拉格朗日参数
params_proc.admm.lambda_sparsity = 0.1; % 稀疏正则化权重 (经验值, 可调整)
params_proc.admm.max_iter = 2;    % ADMM最大迭代次数
params_proc.admm.tol = 1e-2;       % 收敛容限

% 融合/全局迭代参数
params_proc.fusion.max_global_iter = 3; % 全局迭代次数 (VMD与相位估计之间)
params_proc.fusion.global_tol = 1e-2;   % 全局收敛容限
params_proc.fusion.alpha_vmd_guidance = 0.5; % VMD引导中相位先验的权重 (原alpha2)

% 其他处理参数
params_proc.num_azimuth = sim_params.Num_tm; % 方位单元数
params_proc.num_range_bins = sim_params.Num_r; % 距离单元数
params_proc.PRF = sim_params.PRF;
params_proc.fc = sim_params.fc;
params_proc.c = sim_params.c;
params_proc.tm_azimuth = sim_params.tm; % 慢时间轴

% -------------------- 3. 执行ISAR成像算法 -------------------- %
fprintf('开始执行VMD-ADMM-DCFT ISAR成像算法...\n');
tic;
[ISAR_image_sparse, s_compensated_dominant_mode] = perform_isar_imaging_va_dcft(echo_data, params_proc);
fprintf('ISAR成像处理完毕。耗时: %.2f 秒\n', toc);

% -------------------- 4. 显示结果 -------------------- %
fprintf('显示成像结果...\n');

% 显示距离压缩后的原始回波
figure;
imagesc(sim_params.tm, sim_params.r_axis, abs(echo_data));
xlabel('慢时间 (秒)');
ylabel('距离 (米)');
title('距离压缩后的原始回波 (幅度)');
colorbar;
axis xy;

% 正确应用fftshift并保存结果
ISAR_image_sparse_shifted = fftshift(ISAR_image_sparse, 2);

% 显示稀疏重建后的ISAR图像（使用正确的频率轴）
figure;
doppler_axis = linspace(-params_proc.PRF/2, params_proc.PRF/2, params_proc.num_azimuth);
imagesc(doppler_axis, sim_params.r_axis, abs(ISAR_image_sparse_shifted));
xlabel('多普勒频率 (Hz)');
ylabel('距离 (米)');
title('VMD-ADMM-OptimizedDCFT ISAR 成像结果 (稀疏)');
colorbar;
axis xy;

% (可选) 显示仅用主导模态补偿后的FFT结果
ISAR_image_fft_dominant = fftshift(fft(s_compensated_dominant_mode, [], 2), 2);
figure;
imagesc(doppler_axis, sim_params.r_axis, abs(ISAR_image_fft_dominant));
xlabel('多普勒频率 (Hz)');
ylabel('距离 (米)');
title('主导模态补偿 + FFT 成像结果');
colorbar;
axis xy;

% 计算并显示图像质量指标
contrast_val = calculate_image_contrast(abs(ISAR_image_sparse_shifted));
entropy_val = calculate_image_entropy(abs(ISAR_image_sparse_shifted));
fprintf('稀疏图像质量指标:\n');
fprintf(' - 对比度: %.4f\n', contrast_val);
fprintf(' - 熵: %.4f\n', entropy_val);

% 直接FFT结果对比（无相位补偿）
raw_fft = fftshift(fft(echo_data, [], 2), 2);
figure;
imagesc(doppler_axis, sim_params.r_axis, abs(raw_fft));
xlabel('多普勒频率 (Hz)');
ylabel('距离 (米)');
title('原始数据直接FFT结果（无相位补偿）');
colorbar;
axis xy;

% 归一化显示（对数尺度，用于更好可视化）
G1 = 20*log10(abs(ISAR_image_sparse_shifted)./max(abs(ISAR_image_sparse_shifted(:))));
figure('name','稀疏重建结果 (对数尺度)');
imagesc(doppler_axis, sim_params.r_axis, G1);
caxis([-30,0]);
grid on;
axis xy;
colorbar;
xlabel('多普勒频率 (Hz)');
ylabel('距离 (m)');
colormap jet;

G2 = 20*log10(abs(ISAR_image_fft_dominant)./max(abs(ISAR_image_fft_dominant(:))));
figure('name','主导模态补偿 + FFT结果 (对数尺度)');
imagesc(doppler_axis, sim_params.r_axis, G2);
caxis([-30,0]);
grid on;
axis xy;
colorbar;
xlabel('多普勒频率 (Hz)');
ylabel('距离 (m)');
colormap jet;

G3 = 20*log10(abs(raw_fft)./max(abs(raw_fft(:))));
figure('name','直接FFT结果 (对数尺度)');
imagesc(doppler_axis, sim_params.r_axis, G3);
caxis([-30,0]);
grid on;
axis xy;
colorbar;
xlabel('多普勒频率 (Hz)');
ylabel('距离 (m)');
colormap jet;

fprintf('处理完成。\n');



% generate_simulated_echo.m
% 生成ISAR仿真回波数据

function [s_r_tm2, sim_params] = generate_simulated_echo()

% 目标散射点模型 
Pos = [-10 -1 0;-9 -1 0;-8 -1 0;-7 -1 0;-6 -1 0;-5 -1 0;-3.75 -1 0;-3 -1 0;-2 -1 0;-1 -1 0;...
       0 -1 0;...
       1 -1 0;2 -1 0;3 -1 0;4 -1 0;5 -1 0;6 -1 0;7 -1 0;8 -1 0;9 -1 0;10 -1 0;...
       -9.5 0.2 0.5;...
       -9.5 1.2 0.5;-9 1 0;-8 1 0;-7 1 0;-6 1 0;-5.2 1.2 0;-4.1 1 0;-3 1 0;-2 1 0;-1 1 0;...
       0 1 0;...
       1 1 0;2 1 0;3 1 0;4 1 0;5 1 0;6 1 0;7 1 0;8 1 0;9 1 0;10 1 0;...
       10.5 -0.75 0;10.5 0.75 0;11 -0.5 0;11 0.5 0;11.5 0 0;... % 尾部
       9.5 0.5 0.5;9.5 -0.5 0.5;9 0 0.5;8.5 0.5 0.5;8.5 -0.5 0.5;... % 头部
       5 0 0.5;5 0 1;5 0 1.5;5 0 2;5 0 2.5;5 0 3;5 0 3.5;5 0 4;...   % 机头
       5.5 0.5 0.5;5.5 -0.5 0.5;4.5 0.5 0.5;4.5 -0.5 0.5;... % 机头运动
       0.5 0.5 0.9;0.5 -0.5 0.9;-0.5 0.5 0.9;-0.5 -0.5 0.9;0 0 0.5;... % 中间运动
       -5 0 0.2;-5 0.2 0.8;-5 0.2 1.4;-5 0 2;... % 机尾部
       -5.5 -0.5 0.5;-5.5 0.5 0.5;-4.4 0.5 0.5;-4.5 -0.6 0.5;...% 机尾运动
       ];

% 坐标变换和显示
x_Pos_orig = Pos(:,1)*5;
y_Pos_orig = Pos(:,2)*5;
z_Pos_orig = Pos(:,3)*5;

% 用户原始代码中的坐标平移：
% min_x_Pos = min(x_Pos_orig); x_Pos = x_Pos_orig + min_x_Pos;
% min_y_Pos = min(y_Pos_orig); y_Pos = y_Pos_orig + min_y_Pos;
% min_z_Pos = min(z_Pos_orig); z_Pos = z_Pos_orig + min_z_Pos;
% 通常的归一化是减去最小值使最小为0，例如 x_Pos = x_Pos_orig - min(x_Pos_orig);
% 这里遵循用户代码的逻辑，尽管它可能不寻常。
x_Pos = x_Pos_orig; % 使用原始坐标或按需调整
y_Pos = y_Pos_orig;
z_Pos = z_Pos_orig;
% 若要使所有坐标非负且从0开始，可以取消注释以下行：
% x_Pos = x_Pos - min(x_Pos);
% y_Pos = y_Pos - min(y_Pos);
% z_Pos = z_Pos - min(z_Pos);


% figure; plot3(x_Pos,y_Pos,z_Pos,'*'); grid on;
% xlabel('X (m)'); ylabel('Y (m)'); zlabel('Z (m)'); title('目标散射点模型 (仿真用)');

R_los = [cos(3*pi/8)*cos(0), cos(3*pi/8)*sin(0), sin(3*pi/8)]; % 雷达目标视线单位矢量
Num_point = size(x_Pos, 1); % 目标点数

% 投影到垂直于视线的平面，用于计算旋转引起的径向速度等
x_r_proj = zeros(1,Num_point);
y_r_proj = zeros(1,Num_point);
z_r_proj = zeros(1,Num_point);
for n_point = 1:Num_point
    x_r_proj(n_point) = y_Pos(n_point)*R_los(3) - z_Pos(n_point)*R_los(2);
    y_r_proj(n_point) = z_Pos(n_point)*R_los(1) - x_Pos(n_point)*R_los(3);
    z_r_proj(n_point) = x_Pos(n_point)*R_los(2) - y_Pos(n_point)*R_los(1);
end

% 目标旋转参数 (来自用户)
x_omega = 0.05; %0.05 目标旋转初始角速度 (rad/s)
y_omega = 0.2;%0.2
z_omega = 0.05;%0.05
x_alpha_rot = 0.05; % 0.05目标旋转角加速度 (rad/s^2)
y_alpha_rot = 0.1;%0.1
z_alpha_rot = 0.05;%0.05
x_beta_rot = 0.05; % 0.05目标旋转角加加速度 (rad/s^3)
y_beta_rot = 0.2;%0.4
z_beta_rot = 0.05;%0.05

% 每个散射点的径向运动系数 (f_v, alpha_v, beta_v -> 对应距离变化中的 t, t^2, t^3 系数)
f_v_coeffs = zeros(1,Num_point);      % 速度项系数 (m/s)
alpha_v_coeffs = zeros(1,Num_point);  % 加速度项系数 (m/s^2)
beta_v_coeffs = zeros(1,Num_point);   % 加加速度项系数 (m/s^3)

for n_point = 1:Num_point
    f_v_coeffs(n_point) = x_r_proj(n_point)*x_omega + y_r_proj(n_point)*y_omega + z_r_proj(n_point)*z_omega;
    alpha_v_coeffs(n_point) = x_r_proj(n_point)*x_alpha_rot + y_r_proj(n_point)*y_alpha_rot + z_r_proj(n_point)*z_alpha_rot;
    beta_v_coeffs(n_point) = x_r_proj(n_point)*x_beta_rot + y_r_proj(n_point)*y_beta_rot + z_r_proj(n_point)*z_beta_rot;
end

% 雷达系统参数与时域变量 
sim_params.B = 80*1e6;     % 带宽 (Hz)
sim_params.c = 3*1e8;      % 光速 (m/s)
sim_params.PRF = 1400;     % 脉冲重复频率 (Hz)
sim_params.fc = 5.2*1e9;   % 载频 (Hz)
delta_r_res = sim_params.c / (2*sim_params.B); % 距离分辨率
sim_params.r_axis = (-50*delta_r_res : delta_r_res : 50*delta_r_res); % 距离单元轴
sim_params.tm = (0 : (1/sim_params.PRF) : 0.501); % 慢时间轴

sim_params.Num_r = length(sim_params.r_axis);
sim_params.Num_tm = length(sim_params.tm);
ones_r_vec = ones(1, sim_params.Num_r);
ones_tm_vec = ones(1, sim_params.Num_tm);

s_r_tm2 = zeros(sim_params.Num_r, sim_params.Num_tm); % 初始化回波矩阵

Delta_R0_init = zeros(1,Num_point); % 初始时刻的距离值

fprintf('  逐散射点生成回波...\n');
for n_point = 1:Num_point
    % 初始径向距离
    Delta_R0_init(n_point) = x_Pos(n_point)*R_los(1) + y_Pos(n_point)*R_los(2) + z_Pos(n_point)*R_los(3);

    % 瞬时径向距离 (修正用户代码中的多项式构造)
    % R(t) = R0 + v*t + 0.5*a*t^2 + (1/6)*j*t^3
    Delta_R_t = Delta_R0_init(n_point) + ...
                  f_v_coeffs(n_point) .* sim_params.tm + ...
                  (1/2) * alpha_v_coeffs(n_point) .* sim_params.tm.^2 + ...
                  (1/6) * beta_v_coeffs(n_point) .* sim_params.tm.^3;

    % 相位项: phi(t) = (4*pi*fc/c) * R(t)
    phase_term = (4*pi*sim_params.fc/sim_params.c) * Delta_R_t;

    % 幅度因子 (来自用户第一个代码块的逻辑)
    amplitude_factor = 1.0;
    if n_point > 53 && n_point < 62 % 特定点增强
        amplitude_factor = 1.3;
    end
    % 用户代码中对点48的特殊处理: amplitude_factor = amplitude_factor * 1;
    % 这行在 amplitude_factor=1 时无效果。如果点48有特殊幅度，应明确指定。
    % 这里假设点48若不在53-62范围内，则为1.0。

    % 生成单个散射点的回波并累加
    % sinc((2*B/c)*(r - R(t)))
    s_r_tm2 = s_r_tm2 + amplitude_factor * ...
              sinc((2*sim_params.B/sim_params.c) * (sim_params.r_axis.' * ones_tm_vec - ones_r_vec.' * Delta_R_t)) .* ...
              exp(1j * ones_r_vec.' * phase_term);
    if mod(n_point, 20) == 0
        fprintf('    已处理 %d / %d 个散射点\n', n_point, Num_point);
    end
end
fprintf('  所有散射点回波生成完毕。\n');

end

% sinc 函数定义 (MATLAB的sinc是 sin(pi*x)/(pi*x))
% function y = sinc(x)
%     y = ones(size(x));
%     idx = x~=0;
%     y(idx) = sin(pi*x(idx))./(pi*x(idx));
% end


% perform_isar_imaging_va_dcft.m
% 实现VMD-ADMM-OptimizedDCFT ISAR成像算法

% perform_isar_imaging_va_dcft.m
% 实现VMD-ADMM-OptimizedDCFT ISAR成像算法

% perform_isar_imaging_va_dcft.m
% 实现VMD-ADMM-OptimizedDCFT ISAR成像算法

function [ISAR_image_sparse, s_compensated_dominant_mode] = perform_isar_imaging_va_dcft(radar_data, params_proc)

% 1. 参数初始化
[num_range_bins, num_azimuth] = size(radar_data);
fprintf('  处理数据尺寸: %d x %d\n', num_range_bins, num_azimuth);

% 从params_proc中提取参数
K_vmd = params_proc.vmd.K;
tm_normalized = (0:num_azimuth-1) / num_azimuth; % VMD和相位估计内部使用的归一化时间

% 初始化输出
ISAR_image_sparse = zeros(num_range_bins, num_azimuth, 'like', 1j*radar_data(1));
s_compensated_dominant_mode = zeros(size(radar_data), 'like', radar_data); % 用于可选的非稀疏对比

% 归一化输入数据 (逐距离单元处理时内部进行，或全局进行)
% radar_data = radar_data / max(abs(radar_data(:))); % 可选的全局归一化

% 2. 主循环: 处理每个距离单元
fprintf('  开始逐距离单元处理 (共 %d 个)...\n', num_range_bins);
parfor r_idx = 1:num_range_bins % 使用 parfor 进行并行处理
    if mod(r_idx, round(num_range_bins/10)) == 0 && r_idx > 1
        fprintf('    正在处理距离单元: %d/%d\n', r_idx, num_range_bins);
    end

    signal_orig = radar_data(r_idx, :); % 当前距离单元的信号

    % 跳过能量过低的距离单元
    if sum(abs(signal_orig).^2) < 1e-12 % 能量阈值
        ISAR_image_sparse(r_idx, :) = fft(signal_orig); % 或置零
        s_compensated_dominant_mode(r_idx, :) = signal_orig;
        continue;
    end

    % 对当前信号进行归一化，避免数值问题
    signal_norm_factor = max(abs(signal_orig));
    if signal_norm_factor == 0, signal_norm_factor = 1; end
    signal = signal_orig / signal_norm_factor;

    % 初始化VMD模态和中心频率
    u_k = zeros(K_vmd, num_azimuth, 'like', 1j*signal(1));
    omega_k_vmd = zeros(K_vmd, 1); 
    % VMD初始化可以更智能，例如基于FFT峰值
    if strcmp(params_proc.vmd.init_omega_method, 'peaks')
        [~, locs] = findpeaks(abs(fft(signal)), 'SortStr', 'descend', 'NPeaks', K_vmd);
        if ~isempty(locs)
            omega_k_vmd(1:length(locs)) = (locs-1)/num_azimuth;
        end
        % 补齐剩余的 omega_k
        if length(locs) < K_vmd
            remaining_indices = (length(locs)+1):K_vmd;
            omega_k_vmd(remaining_indices) = linspace(0.1, 0.4, length(remaining_indices))'; % 均匀分布
        end
    else % linear
         for k_idx = 1:K_vmd
            omega_k_vmd(k_idx) = (k_idx-1)/(K_vmd); % 简单线性初始化
         end
    end


    % 初始化相位多项式系数 [fd, ka, kb] (对应相位中的 t, t^2, t^3)
    % 这些系数是针对归一化时间 tm_normalized 的
    poly_coeffs_k = zeros(K_vmd, params_proc.phase_est.poly_order); 

    % 初始化ADMM变量 (对于当前距离单元)
    X_admm = fft(signal); % 初始稀疏重建结果 (频谱)
    Z_admm = X_admm;      % 辅助变量
    Lambda_admm_dual = zeros(size(X_admm), 'like', 1j*signal(1)); % 拉格朗日乘子 (对偶变量)

    phi_k_phase_model = zeros(K_vmd, num_azimuth); % 存储每个模态的相位模型 phi(t)

    % 3. 联合迭代优化 (VMD <-> 相位估计)
    for global_iter = 1:params_proc.fusion.max_global_iter
        signal_sum_prev_iter = sum(u_k, 1); % 用于收敛检查

        % 3.1 VMD引导的信号分解
        % 'phi_k_phase_model' 是上一轮迭代得到的相位模型，用于引导VMD
        [u_k, omega_k_vmd, ~] = vmd_decompose(signal, params_proc.vmd, u_k, omega_k_vmd, phi_k_phase_model, params_proc.fusion.alpha_vmd_guidance);

        % 3.2 优化后的相位参数估计 (逐模态)
        for k_idx = 1:K_vmd
            if sum(abs(u_k(k_idx,:))) < 1e-6 % 跳过低能量模态
                poly_coeffs_k(k_idx,:) = 0;
                phi_k_phase_model(k_idx,:) = 0;
                continue;
            end
            % 'poly_coeffs_k(k_idx,:)' 是该模态上一轮的系数，用于初始化当前估计
            [estimated_coeffs, estimated_phase] = estimate_phase_polynomial(u_k(k_idx,:), ...
                                                              poly_coeffs_k(k_idx,:), ...
                                                              params_proc.phase_est, ...
                                                              tm_normalized, params_proc.PRF);
            poly_coeffs_k(k_idx,:) = estimated_coeffs;
            phi_k_phase_model(k_idx,:) = estimated_phase;
        end

        % 3.3 全局收敛检查
        signal_sum_current_iter = sum(u_k, 1);
        if global_iter > 1
            change = norm(signal_sum_current_iter - signal_sum_prev_iter) / (norm(signal_sum_prev_iter) + eps);
            if change < params_proc.fusion.global_tol
                % fprintf('  距离单元 %d: 全局迭代在第 %d 次收敛。\n', r_idx, global_iter);
                break;
            end
        end
    end % end global iteration

    % 4. 基于最终的u_k和phi_k_phase_model进行ADMM稀疏重建
    %   首先构造补偿并叠加后的信号 Y
    Y_compensated_sum = zeros(1, num_azimuth, 'like', 1j*signal(1));
    for k_idx = 1:K_vmd
        Y_compensated_sum = Y_compensated_sum + u_k(k_idx,:) .* exp(-1j * phi_k_phase_model(k_idx,:));
    end
    Y_fft = fft(Y_compensated_sum);

    %   执行ADMM
    [X_reconstructed_spectrum, ~, ~] = admm_reconstruct(Y_fft, params_proc.admm, X_admm, Z_admm, Lambda_admm_dual);

    % 存储当前距离单元的稀疏频谱 (未移位)
    ISAR_image_sparse(r_idx, :) = X_reconstructed_spectrum * signal_norm_factor; % 恢复幅度

    % (可选) 保存用主导模态相位补偿的原始信号，用于对比
    mode_energies = sum(abs(u_k).^2, 2);
    [~, dominant_idx] = max(mode_energies);
    dominant_phase_compensation = phi_k_phase_model(dominant_idx, :);
    s_compensated_dominant_mode(r_idx, :) = signal_orig .* exp(-1j * dominant_phase_compensation);

end % end parfor r_idx

fprintf('  所有距离单元处理完毕。\n');

end

% vmd_decompose.m
% 变分模态分解 (Variational Mode Decomposition)
% 基于K. Dragomiretskiy, D. Zosso, "Variational Mode Decomposition", IEEE Trans. Sig. Proc., 2014.
% 可选地加入相位引导

function [u, omega_modes, lambda_dual] = vmd_decompose(signal, params_vmd, u_init, omega_init, guidance_phi_k, alpha_guidance)
% 输入:
%   signal       - 输入信号 (1D)
%   params_vmd   - VMD参数结构体: alpha, tau, K, tol, max_iter
%   u_init       - 初始模态 (K x N)
%   omega_init   - 初始中心频率 (K x 1)
%   guidance_phi_k - (可选) 引导相位模型 (K x N), 每个模态的 phi(t)
%   alpha_guidance - (可选) 引导项的权重
% 输出:
%   u            - 分解后的模态 (K x N)
%   omega_modes  - 各模态的中心频率 (K x 1)
%   lambda_dual  - 拉格朗日对偶变量

% 参数提取
alpha = params_vmd.alpha; % 带宽限制参数
tau = params_vmd.tau;     % 对偶上升步长 (噪声容忍度, 0表示无噪声)
K = params_vmd.K;         % 模态数量
tol = params_vmd.tol;     % 收敛容限
max_iter = params_vmd.max_iter;

if nargin < 5
    guidance_phi_k = [];
    alpha_guidance = 0;
end
if isempty(guidance_phi_k)
    alpha_guidance = 0;
end


N = length(signal);
signal_fft = fft(signal);
f_axis_normalized = (0:N-1)/N; % 归一化频率轴

% 初始化模态和频率
u = u_init;
omega_modes = omega_init;
u_fft = zeros(K, N, 'like', 1j*signal(1));
for k_idx = 1:K
    u_fft(k_idx,:) = fft(u(k_idx,:));
end

% 初始化拉格朗日乘子
lambda_dual = zeros(1, N, 'like', 1j*signal(1)); 

% VMD 主迭代
for iter = 1:max_iter
    u_prev_sum_fft = sum(u_fft, 1); % 用于计算残差

    for k_idx = 1:K
        % 计算除当前模态外的所有模态之和的fft
        sum_other_modes_fft = u_prev_sum_fft - u_fft(k_idx,:);

        % 在频域更新 u_k
        % 分子项: signal_fft - sum_other_modes_fft + lambda_dual_fft/2
        numerator_fft = signal_fft - sum_other_modes_fft + lambda_dual/2; % lambda_dual is already in freq domain if tau=0, otherwise it's time domain
                                                                       % Assuming lambda_dual is updated in time domain as per original VMD
                                                                       % So, fft(lambda_dual)/2 if lambda_dual is time domain.
                                                                       % The original paper's ADMM updates lambda in time domain.
                                                                       % If tau > 0, lambda is updated. If tau = 0, lambda is not updated from init.
                                                                       % Let's assume lambda_dual is time-domain lambda_hat in paper.

        if tau > 0 % lambda is updated and is time-domain
            numerator_fft = signal_fft - sum_other_modes_fft + fft(lambda_dual)/2;
        else % lambda is fixed at 0 (or some init), effectively no lambda term if init at 0.
             % If lambda_dual is init as 0, this term vanishes.
            numerator_fft = signal_fft - sum_other_modes_fft + lambda_dual/2; % if lambda_dual is freq-domain Lagrange multiplier
        end


        % 分母项: 1 + 2*alpha*(omega - omega_k)^2
        denominator = 1 + 2*alpha*(f_axis_normalized - omega_modes(k_idx)).^2;

        % 加入引导相位 (如果提供)
        if alpha_guidance > 0 && ~isempty(guidance_phi_k) && size(guidance_phi_k,1) >= k_idx && any(guidance_phi_k(k_idx,:))
            phase_prior_term_fft = fft(exp(1j * guidance_phi_k(k_idx,:))); % 构造相位先验的频谱

            % 加权更新分子和分母
            % This is a heuristic way to incorporate phase prior.
            % Original VMD does not have this. A more rigorous way might involve modifying the objective.
            % One interpretation: add a term to objective || u_k - A*exp(j*phi_k) ||^2
            % In frequency domain, this would be || U_k - FFT(A*exp(j*phi_k)) ||^2
            % The update for U_k would be ( ... + weight * FFT(A*exp(j*phi_k)) ) / ( ... + weight )

            numerator_fft = numerator_fft + alpha_guidance * phase_prior_term_fft;
            denominator = denominator + alpha_guidance;
        end

        u_fft(k_idx,:) = numerator_fft ./ denominator;
        u(k_idx,:) = ifft(u_fft(k_idx,:)); % 更新时域模态

        % 更新中心频率 omega_k (重心法)
        power_spectrum_uk = abs(u_fft(k_idx,:)).^2;
        if sum(power_spectrum_uk) > 1e-10
            omega_modes(k_idx) = sum(f_axis_normalized .* power_spectrum_uk) / sum(power_spectrum_uk);
        else
            % 若模态能量过低，保持不变或重新初始化
            % omega_modes(k_idx) = omega_modes(k_idx); 
        end
    end

    % 更新拉格朗日乘子 lambda_dual (时域)
    if tau > 0 % Only update if tau is non-zero (meaning there's a quadratic penalty for reconstruction error)
        current_sum_u = sum(u, 1);
        lambda_dual = lambda_dual + tau * (signal - current_sum_u);
    end

    % 检查收敛性 (基于模态总和的变化)
    if iter > 1
        u_sum_change = norm(sum(u_fft,1) - u_prev_sum_fft_iter_start) / norm(u_prev_sum_fft_iter_start + eps);
        if u_sum_change < tol
            break;
        end
    end
    u_prev_sum_fft_iter_start = sum(u_fft,1); % Store for next iteration's convergence check

end % end VMD iteration
end % end function


% estimate_phase_polynomial.m
% 估计信号模态的多项式相位系数 (fd, ka, kb)
% 采用分阶段估计策略以提高效率

% estimate_phase_polynomial.m
% 估计信号模态的多项式相位系数 (fd, ka, kb)
% 采用分阶段估计策略，使用物理单位和实际时间轴

% estimate_phase_polynomial.m
% 估计信号模态的多项式相位系数 (fd, ka, kb)
% 采用分阶段估计策略以提高效率

function [poly_coeffs_estimated, phase_estimated] = estimate_phase_polynomial(signal_mode, initial_coeffs, params_phase_est, tm_normalized, PRF)
% 输入:
%   signal_mode       - 单个VMD模态 (1D)
%   initial_coeffs    - [fd0, ka0, kb0] 初始系数估计 (用于迭代优化或范围中心)
%   params_phase_est  - 相位估计参数: poly_order, fd_search_range_factor, etc.
%   tm_normalized     - 归一化慢时间轴 (0 to N-1)/N
%   PRF               - 脉冲重复频率 (用于fd的实际单位转换和搜索范围)
% 输出:
%   poly_coeffs_estimated - [fd_est, ka_est, kb_est] 估计的系数 (针对 tm_normalized)
%   phase_estimated       - 2*pi*(fd*t + 0.5*ka*t^2 + (1/6)*kb*t^3) 估计的相位

N = length(signal_mode);
poly_order = params_phase_est.poly_order;
poly_coeffs_estimated = zeros(1, poly_order);

% --- 1. 估计 fd (线性项系数, 多普勒中心) ---
%    方法: FFT峰值检测
signal_mode_fft = fft(signal_mode);
[~, idx_max_fft] = max(abs(signal_mode_fft));
fd_est_norm = (idx_max_fft - 1) / N; % 归一化频率 (0 to 1)
% 将fd_est_norm调整到 [-0.5, 0.5] 范围 (如果需要)
if fd_est_norm > 0.5 
    fd_est_norm = fd_est_norm - 1;
end
poly_coeffs_estimated(1) = fd_est_norm; % 这是针对归一化时间的fd

% --- 2. 估计 ka (二次项系数, 线性调频率) ---
if poly_order >= 2
    % 补偿fd
    signal_comp_fd = signal_mode .* exp(-1j * 2*pi * poly_coeffs_estimated(1) * tm_normalized);

    % 搜索ka: 最大化去啁啾后信号频谱的能量集中度 (例如，对比度或峭度)
    % ka_search_range_norm: ka的范围需要根据信号特性确定，这里用一个启发式范围
    % ka的物理意义: 频率变化率。PRF^2 * (ka_norm) 约等于实际的 chirp rate in Hz/s
    % 一个粗略的ka范围可以基于fd的变化或经验
    max_chirp_rate_hz_s = (PRF/2)^2; % 假设最大频率变化能达到 (PRF/2) over 0.5s 
    ka_norm_max_abs = max_chirp_rate_hz_s / PRF^2 * 0.5; % 对应于归一化时间单位的ka
                                                      % (d(fd_norm)/dt_norm) = ka_norm
    ka_search_values = linspace(-ka_norm_max_abs*2, ka_norm_max_abs*2, params_phase_est.ka_search_pts); % 示例范围

    sharpness_ka = zeros(size(ka_search_values));
    for i_ka = 1:length(ka_search_values)
        ka_current = ka_search_values(i_ka);
        dechirped_signal = signal_comp_fd .* exp(-1j * 2*pi * 0.5 * ka_current * tm_normalized.^2);
        spectrum_dechirped = abs(fft(dechirped_signal)).^2;
        sharpness_ka(i_ka) = sum(spectrum_dechirped.^2); % L4 norm as sharpness (or use contrast)
    end
    [~, idx_max_ka] = max(sharpness_ka);
    poly_coeffs_estimated(2) = ka_search_values(idx_max_ka);
else
    if poly_order >=2; poly_coeffs_estimated(2) = 0; end
end

% --- 3. 估计 kb (三次项系数) ---
if poly_order >= 3
    % 补偿fd和ka
    signal_comp_fd_ka = signal_mode .* exp(-1j * 2*pi * (poly_coeffs_estimated(1) * tm_normalized + ...
                                                       0.5 * poly_coeffs_estimated(2) * tm_normalized.^2));

    % 搜索kb: 类似于ka的搜索
    % kb的物理意义: 频率变化率的变化率。PRF^3 * (kb_norm) 约等于实际 Hz/s^2
    kb_norm_max_abs = (PRF/2)^3 / PRF^3 * 0.2; % 启发式范围
    kb_search_values = linspace(-kb_norm_max_abs*2, kb_norm_max_abs*2, params_phase_est.kb_search_pts); % 示例范围

    sharpness_kb = zeros(size(kb_search_values));
    for i_kb = 1:length(kb_search_values)
        kb_current = kb_search_values(i_kb);
        decubic_signal = signal_comp_fd_ka .* exp(-1j * 2*pi * (1/6) * kb_current * tm_normalized.^3);
        spectrum_decubic = abs(fft(decubic_signal)).^2;
        sharpness_kb(i_kb) = sum(spectrum_decubic.^2);
    end
    [~, idx_max_kb] = max(sharpness_kb);
    poly_coeffs_estimated(3) = kb_search_values(idx_max_kb);
else
    if poly_order >=3; poly_coeffs_estimated(3) = 0; end
end

% --- 构建最终的估计相位 ---
phase_estimated = zeros(size(tm_normalized));
if poly_order >= 1
    phase_estimated = phase_estimated + 2*pi * poly_coeffs_estimated(1) * tm_normalized;
end
if poly_order >= 2
    phase_estimated = phase_estimated + 2*pi * 0.5 * poly_coeffs_estimated(2) * tm_normalized.^2;
end
if poly_order >= 3
    phase_estimated = phase_estimated + 2*pi * (1/6) * poly_coeffs_estimated(3) * tm_normalized.^3;
end
% Higher orders can be added if poly_order allows

end



% admm_reconstruct.m
% 基于ADMM的稀疏重建 (L1范数最小化)

function [X_sparse, Z_sparse, Lambda_dual] = admm_reconstruct(Y_fft, params_admm, X_init, Z_init, Lambda_init)
% 输入:
%   Y_fft        - 观测到的频谱 (经VMD分解、相位补偿、叠加后FFT的结果)
%   params_admm  - ADMM参数: rho, lambda_sparsity, max_iter, tol
%   X_init       - X 的初始值
%   Z_init       - Z 的初始值
%   Lambda_init  - Lambda (对偶变量) 的初始值
% 输出:
%   X_sparse     - 重建的稀疏频谱
%   Z_sparse     - 辅助稀疏变量
%   Lambda_dual  - 更新后的对偶变量

% ADMM参数
rho = params_admm.rho;
lambda_s = params_admm.lambda_sparsity; % 稀疏项权重
max_iter = params_admm.max_iter;
tol = params_admm.tol;

% 初始化
X = X_init;
Z = Z_init;
Lambda_dual = Lambda_init; % 这是对偶变量 Y 在ADMM文献中常用

% ADMM迭代 (求解 min 0.5*||X - Y_fft||^2 + lambda_s*||Z||_1 s.t. X=Z)
% 这里的 formulation 和原代码的 alpha4, alpha5 有点不同，
% 原代码: (alpha4/2)*||X-Y_fft||^2 + alpha5*||Z||_1
% 如果 alpha4=1, lambda_s = alpha5
% X-update: (Y_fft + rho*Z - Lambda_dual) / (1 + rho) if alpha4=1

for iter = 1:max_iter
    X_prev = X;
    Z_prev = Z;

    % 更新X (数据保真度)
    % L_rho = 0.5*||X - Y_fft||^2 + Lambda_dual^T*(X-Z) + (rho/2)*||X-Z||^2
    % dL/dX = X - Y_fft + Lambda_dual + rho*(X-Z) = 0
    % X(1+rho) = Y_fft - Lambda_dual + rho*Z
    X = (Y_fft - Lambda_dual + rho * Z) / (1 + rho);

    % 更新Z (稀疏性)
    % L_rho = lambda_s*||Z||_1 + Lambda_dual^T*(X-Z) + (rho/2)*||X-Z||^2
    % argmin_Z lambda_s*||Z||_1 + (rho/2)*||Z - (X + Lambda_dual/rho)||^2
    Z = soft_threshold(X + Lambda_dual/rho, lambda_s/rho);

    % 更新Lambda_dual (拉格朗日乘子 / 对偶变量)
    Lambda_dual = Lambda_dual + rho * (X - Z);

    % 收敛检查
    primal_residual = norm(X - Z);
    dual_residual = rho * norm(Z - Z_prev);

    if primal_residual < tol && dual_residual < tol
        % fprintf('    ADMM 在第 %d 次迭代收敛。\n', iter);
        break;
    end
end

X_sparse = X;
Z_sparse = Z;

end

% 辅助函数: 软阈值
function y = soft_threshold(x, threshold_val)
    y = sign(x) .* max(abs(x) - threshold_val, 0);
end
% calculate_image_contrast.m
% 计算图像对比度

function contrast = calculate_image_contrast(image_abs)
% 输入:
%   image_abs - ISAR图像的幅度值

    if isempty(image_abs) || numel(image_abs) < 2
        contrast = 0;
        return;
    end
    mean_val = mean(image_abs(:));
    std_val = std(image_abs(:));

    if mean_val == 0
        contrast = 0; % 或者 NaN，取决于如何定义
    else
        contrast = std_val / mean_val;
    end
end

% calculate_image_entropy.m
% 计算图像熵

function entropy = calculate_image_entropy(image_abs)
% 输入:
%   image_abs - ISAR图像的幅度值

    if isempty(image_abs)
        entropy = NaN;
        return;
    end

    % 归一化图像能量，使其像概率分布
    image_power = image_abs(:).^2;
    sum_power = sum(image_power);

    if sum_power == 0
        entropy = 0; % 或者 NaN
        return;
    end

    normalized_power = image_power / sum_power;

    % 避免 log2(0)
    valid_indices = normalized_power > eps; % eps 是最小的正浮点数

    if ~any(valid_indices)
        entropy = 0;
        return;
    end

    entropy = -sum(normalized_power(valid_indices) .* log2(normalized_power(valid_indices)));
end
G1=20*log10(abs(ISAR_image_sparse_shifted)./max(abs(ISAR_image_sparse_shifted(:))));

figure('name','稀疏');
imagesc(G1);caxis([-34,0]);
grid on;axis xy;colorbar;%axis equal;
% axis([-0.6 0.6 y(select_row(1)) y(select_row(end))]);%set(gca,'xtick',[-0.2 0 0.2]);
xlabel('azimuth');ylabel('range (m)');colormap jet;


G2=20*log10(abs(ISAR_image_fft_dominant)./max(abs(ISAR_image_fft_dominant(:))));
figure('name','DCT成像结果');
imagesc(G2);caxis([-34,0]);
grid on;axis xy;colorbar;%axis equal;
% axis([-0.6 0.6 y(select_row(1)) y(select_row(end))]);%set(gca,'xtick',[-0.2 0 0.2]);
xlabel('azimuth');ylabel('range (m)');colormap jet;


G1=20*log10(abs(raw_fft)./max(abs(raw_fft(:))));
figure('name','DCT成像结果');
imagesc(G1);caxis([-30,0]);
grid on;axis xy;colorbar;%axis equal;
% axis([-0.6 0.6 y(select_row(1)) y(select_row(end))]);%set(gca,'xtick',[-0.2 0 0.2]);
xlabel('azimuth');ylabel('range (m)');colormap jet;


EntropyImage(ISAR_image_sparse_shifted+eps)
contrast(ISAR_image_sparse_shifted)
EntropyImage(raw_fft+eps)
contrast(raw_fft)


