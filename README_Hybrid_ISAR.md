2. 基于VMD的自适应信号分解
VMD算法将输入信号 s(t) 分解为 K 个有限带宽的本征模态函数 (IMFs) u_k(t)，每个IMF u_k(t) 紧凑地围绕其中心频率 \omega_k(t)（对于标准VMD，\omega_k 是常数；对于时变VMD，\omega_k(t) 可以随时间变化）。VMD的优化问题旨在最小化所有模态的估计带宽之和，约束条件是分解后的模态之和能重建原始信号。
对于复信号 s(t) (ISAR信号通常是复数形式)，VMD的优化问题表述如下： \min_{\{u_k\}, \{\omega_k\}} \left\{ \sum_{k=1}^{K} \left\| \partial_t \left[ \left( \delta(t) + \frac{j}{\pi t} \right) * u_k(t) \right] e^{-j\omega_k t} \right\|_2^2 \right\} (3) 约束条件为： s(t) = \sum_{k=1}^{K} u_k(t) 其中，u_k 是第 k 个模态，K 是预设的模态总数，\omega_k 是第 k 个模态的中心频率，\delta(t) 是狄拉克函数，(\delta(t) + j/(\pi t)) * u_k(t) 表示 u_k(t) 的解析信号 u_{k}^{+}(t)，\partial_t 表示对时间 t 的偏导数。\|\cdot\|_2^2 表示L2范数的平方。
为了求解这个约束优化问题，引入二次惩罚项和拉格朗日乘子 \lambda(t)，构造增广拉格朗日函数 \mathcal{L}: \mathcal{L}(\{u_k\}, \{\omega_k\}, \lambda) = \alpha \sum_{k=1}^{K} \left\| \partial_t [u_k^+(t)] e^{-j\omega_k t} \right\|_2^2 + \left\| s(t) - \sum_{k=1}^{K} u_k(t) \right\|_2^2 + \left\langle \lambda(t), s(t) - \sum_{k=1}^{K} u_k(t) \right\rangle (4) 其中 \alpha 是二次惩罚因子。该优化问题通常采用交替方向乘数法 (ADMM) 进行迭代求解。ADMM的迭代更新步骤如下（在频域进行更高效）：
模态更新 u_k^{n+1}: \hat{u}_k^{n+1}(\omega) = \frac{\hat{s}(\omega) - \sum_{i \neq k} \hat{u}_i^{n+1}(\omega) + \frac{\hat{\lambda}^n(\omega)}{2}}{1 + 2\alpha(\omega - \omega_k^n)^2} (5) 这里，\hat{f}(\omega) 表示 f(t) 的傅里叶变换。上式是在 \omega \ge 0 的单边谱上操作。
中心频率更新 \omega_k^{n+1}: \omega_k^{n+1} = \frac{\int_0^\infty \omega |\hat{u}_k^{n+1}(\omega)|^2 d\omega}{\int_0^\infty |\hat{u}_k^{n+1}(\omega)|^2 d\omega} (6)
拉格朗日乘子更新 \lambda^{n+1}: \hat{\lambda}^{n+1}(\omega) = \hat{\lambda}^n(\omega) + \tau \left( \hat{s}(\omega) - \sum_k \hat{u}_k^{n+1}(\omega) \right) (7) 其中 \tau 是更新参数。
通过VMD分解，原始的复杂ISAR回波信号 s(t) 被分解为一组IMF u_k(t)。理想情况下，每个IMF u_k(t) 包含了来自目标上特定散射区域或具有特定运动模式的信号分量。
3. 基于DCFT的IMF参数估计与运动补偿
对于VMD分解得到的每个IMF u_k(t)，我们假设其相位可以用三次多项式来近似，如式(2)所示（下标 p 替换为 k）。离散立方傅里叶变换 (DCFT) 是一种有效的参数估计算法，用于估计立方相位信号的参数。 对于一个IMF u_k(t)，其DCFT定义为： D_k(f, K_a, K_c) = \sum_{m=0}^{M-1} u_k(m T_s) \exp\left[-j2\pi \left( f(mT_s) + \frac{1}{2}K_a(mT_s)^2 + \frac{1}{6}K_c(mT_s)^3 \right)\right] (8) 其中 T_s 是慢时间采样间隔，M 是慢时间采样点数。通过在参数空间 (f, K_a, K_c) 中搜索 D_k(f, K_a, K_c) 的峰值，可以得到IMF u_k(t) 的参数估计值 (\hat{f}_{d,k}, \hat{K}_{a,k}, \hat{K}_{c,k})。 (\hat{f}_{d,k}, \hat{K}_{a,k}, \hat{K}_{c,k}) = \arg \max_{f, K_a, K_c} |D_k(f, K_a, K_c)| (9) 实际中，DCFT通常通过多阶段或特定搜索策略（如乘积高阶模糊函数 PHAF，积分立方相位函数 ICPF 等的变体）来实现，以降低计算复杂度。
一旦获得了IMF u_k(t) 的相位参数估计值，就可以构造补偿相位函数： \phi_{comp,k}(t) = \exp\left[-j2\pi \left( \hat{f}_{d,k}t + \frac{1}{2}\hat{K}_{a,k}t^2 + \frac{1}{6}\hat{K}_{c,k}t^3 \right)\right] (10) 对该IMF进行运动补偿： u'_{k}(t) = u_k(t) \cdot \phi_{comp,k}(t) (11) 补偿后的IMF u'_k(t) 的主要相位调制（由复杂运动引起的多普勒频移和展宽）被移除，理想情况下变为一个近似的单频信号，其剩余相位对应于该散射中心的初始相位和精细的方位位置。
4. 基于ADMM的稀疏方位像重建



方案二：\subsubsection{变分模态分解 (VMD) 优化目标函数}

VMD 算法的核心思想是将信号分解为 $K$ 个具有有限带宽的本征模态函数（IMFs）。对于 ISAR 回波信号 $s(t)$，VMD 的变分问题可表示为：

\begin{equation}
\min_{\{u_k\}, \{\omega_k\}} \left\{ \sum_{k=1}^{K} \left\| \partial_t \left[ \left( \delta(t) + \frac{j}{\pi t} \right) * u_k(t) \right] e^{-j\omega_k t} \right\|_2^2 \right\}
\end{equation}

约束条件为：
\begin{equation}
\sum_{k=1}^{K} u_k(t) = s(t)
\end{equation}

其中：
\begin{itemize}
    \item $u_k(t)$ 为第 $k$ 个模态函数
    \item $\omega_k$ 为第 $k$ 个模态的中心频率
    \item $\delta(t)$ 为狄拉克函数
    \item $(\delta(t) + \frac{j}{\pi t}) * u_k(t)$ 表示 $u_k(t)$ 的解析信号
    \item $\partial_t$ 表示对时间 $t$ 的偏导数
\end{itemize}

\subsubsection{离散立方傅里叶变换 (DCFT) 多项式相位模型}

DCFT 用于处理非线性相位，针对 ISAR 舰船成像中的三维运动，采用四阶多项式相位模型：

\begin{equation}
\phi(t) = \phi_0 + 2\pi f_d t + \pi \alpha t^2 + \frac{2\pi}{3}\beta t^3 + \frac{\pi}{2}\gamma t^4
\end{equation}

其中：
\begin{itemize}
    \item $\phi_0$ 为初始相位
    \item $f_d$ 为多普勒频移
    \item $\alpha$ 为啁啾率，表示加速度引起的相位调制
    \item $\beta$ 为啁啾率导数，表示加加速度引起的相位调制
    \item $\gamma$ 为高阶相位调制项，表示复杂运动引起的相位调制
\end{itemize}

DCFT 的核心是在参数空间 $(f_d, \alpha, \beta, \gamma)$ 搜索使以下表达式最大化的参数组合：

\begin{equation}
D(f_d, \alpha, \beta, \gamma) = \left| \sum_{m=0}^{M-1} s(m\Delta t) \exp\left[-j(\phi_0 + 2\pi f_d m\Delta t + \pi \alpha (m\Delta t)^2 + \frac{2\pi}{3}\beta (m\Delta t)^3 + \frac{\pi}{2}\gamma (m\Delta t)^4) \right] \right|
\end{equation}

\subsubsection{交替方向乘数法 (ADMM) 增广拉格朗日函数}

ADMM 用于解决带约束的优化问题，其标准形式为：

\begin{equation}
\min_{x,z} f(x) + g(z) \quad \text{s.t.} \quad Ax + Bz = c
\end{equation}

对应的增广拉格朗日函数为：

\begin{equation}
\mathcal{L}_\rho(x, z, y) = f(x) + g(z) + y^T(Ax + Bz - c) + \frac{\rho}{2}\|Ax + Bz - c\|_2^2
\end{equation}

迭代更新方程：

\begin{align}
x^{k+1} &= \arg\min_x \mathcal{L}_\rho(x, z^k, y^k) \\
z^{k+1} &= \arg\min_z \mathcal{L}_\rho(x^{k+1}, z, y^k) \\
y^{k+1} &= y^k + \rho(Ax^{k+1} + Bz^{k+1} - c)
\end{align}

\subsection{融合算法的联合优化目标函数}

基于以上三种技术，VMD-ADMM-DCFT 融合算法的联合优化目标函数可表示为：

\begin{equation}
\min_{\{u_k\}, \{\omega_k\}, \{a_k, b_k, c_k, d_k\}, X} \mathcal{J}(\{u_k\}, \{\omega_k\}, \{a_k, b_k, c_k, d_k\}, X)
\end{equation}

其中联合目标函数 $\mathcal{J}$ 定义为：

\begin{equation}
\mathcal{J} = \alpha_1 \sum_{k=1}^{K} \left\| \partial_t [u_k^+(t)] e^{-j\omega_k t} \right\|_2^2 + \alpha_2 \sum_{r=1}^{R}\sum_{k=1}^{K} \left\| u_{k,r}(t) - \hat{u}_{k,r}(t; a_k, b_k, c_k, d_k) \right\|_2^2 + \alpha_3 \|s(t) - \sum_{k=1}^{K} u_k(t)\|_2^2 + \alpha_4 \|X\|_1
\end{equation}

约束条件：

\begin{equation}
\sum_{k=1}^{K} u_k(t) = s(t)
\end{equation}

\begin{equation}
X = \mathcal{F}\{u_k(t) \cdot \exp(-j\phi_k(t))\}
\end{equation}

其中：
\begin{itemize}
    \item $\alpha_1, \alpha_2, \alpha_3, \alpha_4$ 为平衡参数
    \item $u_{k,r}(t)$ 为第 $r$ 个距离单元第 $k$ 个模态
    \item $\hat{u}_{k,r}(t; a_k, b_k, c_k, d_k)$ 为使用 DCFT 参数 $(a_k, b_k, c_k, d_k)$ 建模的模态
    \item $\phi_k(t) = a_k t + b_k t^2 + c_k t^3 + d_k t^4$ 为相位函数
    \item $X$ 为稀疏重建的方位像
    \item $\mathcal{F}$ 表示傅里叶变换
    \item $\|X\|_1$ 为 $L_1$ 范数，用于促进稀疏性
\end{itemize}

\subsection{迭代求解方案}

使用交替优化策略，将联合优化问题分解为三个子问题：

\subsubsection{VMD-引导的信号分解（固定 DCFT 参数和稀疏重建）}

\begin{equation}
\min_{\{u_k\}, \{\omega_k\}} \alpha_1 \sum_{k=1}^{K} \left\| \partial_t [u_k^+(t)] e^{-j\omega_k t} \right\|_2^2 + \alpha_3 \|s(t) - \sum_{k=1}^{K} u_k(t)\|_2^2
\end{equation}

引入 DCFT 参数 $\{a_k, b_k, c_k, d_k\}$ 指导 VMD 初始化，模态更新公式为：

\begin{equation}
\hat{u}_k^{n+1}(\omega) = \frac{\hat{s}(\omega) - \sum_{i \neq k} \hat{u}_i^{n+1}(\omega) + \frac{\hat{\lambda}^n(\omega)}{2}}{1 + 2\alpha_1(\omega - \omega_k^n)^2}
\end{equation}

中心频率更新：

\begin{equation}
\omega_k^{n+1} = \frac{\int_0^\infty \omega |\hat{u}_k^{n+1}(\omega)|^2 d\omega}{\int_0^\infty |\hat{u}_k^{n+1}(\omega)|^2 d\omega}
\end{equation}

\subsubsection{DCFT-增强的相位估计（固定模态和稀疏重建）}

\begin{equation}
\min_{\{a_k, b_k, c_k, d_k\}} \alpha_2 \sum_{r=1}^{R}\sum_{k=1}^{K} \left\| u_{k,r}(t) - \hat{u}_{k,r}(t; a_k, b_k, c_k, d_k) \right\|_2^2
\end{equation}

对每个模态，使用 DCFT 搜索最优相位参数，目标函数为：

\begin{equation}
\max_{a_k, b_k, c_k, d_k} \left| \sum_{m=0}^{M-1} u_k(m\Delta t) \exp\left[-j(a_k m\Delta t + b_k (m\Delta t)^2 + c_k (m\Delta t)^3 + d_k (m\Delta t)^4) \right] \right|
\end{equation}

\subsubsection{ADMM-约束的稀疏重建（固定模态和相位参数）}

\begin{equation}
\min_X \alpha_4 \|X\|_1 \quad \text{s.t.} \quad X = \mathcal{F}\{u_k(t) \cdot \exp(-j\phi_k(t))\}
\end{equation}

引入辅助变量 $Z$ 和拉格朗日乘子 $\Lambda$，增广拉格朗日函数为：

\begin{equation}
\mathcal{L}_\rho(X, Z, \Lambda) = \alpha_4 \|Z\|_1 + \langle \Lambda, X - Z \rangle + \frac{\rho}{2}\|X - Z\|_F^2
\end{equation}

ADMM 迭代更新：

\begin{align}
X^{k+1} &= (\mathcal{F}\{u_k(t) \cdot \exp(-j\phi_k(t))\} + \rho Z^k - \Lambda^k) / (1 + \rho) \\
Z^{k+1} &= \mathcal{S}_{\alpha_4/\rho}(X^{k+1} + \Lambda^k/\rho) \\
\Lambda^{k+1} &= \Lambda^k + \rho(X^{k+1} - Z^{k+1})
\end{align}

其中 $\mathcal{S}_\tau(x)$ 为软阈值算子：$\mathcal{S}_\tau(x) = \text{sign}(x) \cdot \max(|x| - \tau, 0)$

\subsection{融合更新策略}

三个子问题交替求解后，使用加权融合更新当前结果：

\begin{equation}
s_{fused}^{n+1} = w_{vmd} \cdot s_{vmd}^{n+1} + w_{dcft} \cdot s_{dcft}^{n+1} + w_{admm} \cdot s_{admm}^{n+1}
\end{equation}

其中 $w_{vmd}$, $w_{dcft}$, $w_{admm}$ 为融合权重，$\sum_{i} w_i = 1$

收敛判断准则：

\begin{equation}
\frac{\|s_{fused}^{n+1} - s_{fused}^{n}\|_F}{\|s_{fused}^{n}\|_F} < \epsilon
\end{equation}

\section{逐步可行性分析}

\subsection{算法理论依据与数学基础}

\subsubsection{VMD-引导的 ADMM 策略}

VMD 提供了信号自适应分解能力，能将复杂回波分解为具有明确物理意义的模态，但其分解过程中没有考虑稀疏约束和具体相位结构。选择 VMD 引导 ADMM 的依据在于：

\begin{itemize}
    \item VMD 为 ADMM 提供良好的初始值，使稀疏优化从更合理的初始点开始。
    \item VMD 的模态分解能捕获不同散射中心的特性，为后续 ADMM 稀疏重建提供先验信息。
    \item 模态分解能将不同距离单元的相似运动特性分组，降低优化复杂度。
\end{itemize}