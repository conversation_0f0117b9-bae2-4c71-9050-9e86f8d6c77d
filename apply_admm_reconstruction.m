% =========================================================================
% apply_admm_reconstruction.m
% ADMM稀疏重建函数，提高ISAR图像质量
% =========================================================================
function ISAR_image_enhanced = apply_admm_reconstruction(s_compensated, params)
    % 获取数据尺寸
    [num_range_bins, num_azimuth] = size(s_compensated);
    
    % 初始化输出图像
    ISAR_image_enhanced = zeros(size(s_compensated), 'like', 1j*s_compensated(1));
    
    % ADMM参数
    rho = params.admm_rho;
    lambda_s = params.admm_lambda;
    max_iter = params.admm_max_iter;
    tol = params.admm_tol;
    
    % 逐距离单元处理
    parfor r_idx = 1:num_range_bins
        % 获取当前距离单元补偿后的信号
        signal = s_compensated(r_idx, :);
        
        % 跳过能量过低的距离单元
        if sum(abs(signal).^2) < 1e-10
            ISAR_image_enhanced(r_idx, :) = fft(signal);
            continue;
        end
        
        % 对每个距离单元执行ADMM重建
        [X_reconstructed, ~, ~] = admm_complex_sparse_reconstruction(signal, rho, lambda_s, max_iter, tol);
        
        % 存储重建结果
        ISAR_image_enhanced(r_idx, :) = X_reconstructed;
    end
    
    % 应用fftshift
    ISAR_image_enhanced = fftshift(ISAR_image_enhanced, 2);
end

% ADMM核心函数
function [X_sparse, Z_sparse, Lambda_dual] = admm_complex_sparse_reconstruction(signal, rho, lambda_s, max_iter, tol)
    % 计算信号的傅里叶变换作为初始估计
    Y_fft = fft(signal);
    N = length(signal);
    
    % 初始化变量
    X = Y_fft;
    Z = X;
    Lambda_dual = zeros(size(X), 'like', 1j*X(1));
    
    % 自适应参数调整
    rho_adaptive = rho;
    lambda_s_adaptive = lambda_s;
    
    % 估计噪声水平
    signal_energy = sum(abs(signal).^2);
    sorted_Y = sort(abs(Y_fft));
    noise_level = mean(sorted_Y(1:floor(N/4)));
    
    % 调整稀疏参数
    snr_est = signal_energy / (N * noise_level^2 + eps);
    if snr_est > 20
        lambda_s_adaptive = lambda_s_adaptive * 0.8;
    elseif snr_est < 5
        lambda_s_adaptive = lambda_s_adaptive * 1.2;
    end
    
    % 主迭代循环
    for iter = 1:max_iter
        % 保存上一次迭代的Z
        Z_prev = Z;
        
        % 更新X
        X = (Y_fft + rho_adaptive * Z - Lambda_dual) / (1 + rho_adaptive);
        
        % 更新Z (应用软阈值)
        Z_tilde = X + Lambda_dual / rho_adaptive;
        Z = apply_complex_soft_threshold(Z_tilde, lambda_s_adaptive / rho_adaptive);
        
        % 更新Lambda
        Lambda_dual = Lambda_dual + rho_adaptive * (X - Z);
        
        % 检查收敛性
        primal_residual = norm(X - Z);
        dual_residual = rho_adaptive * norm(Z - Z_prev);
        
        % 自适应调整rho
        if primal_residual > 10 * dual_residual
            rho_adaptive = rho_adaptive * 2;
            Lambda_dual = Lambda_dual / 2;
        elseif dual_residual > 10 * primal_residual
            rho_adaptive = rho_adaptive / 2;
            Lambda_dual = Lambda_dual * 2;
        end
        
        % 检查收敛
        if (primal_residual < tol) && (dual_residual < tol)
            break;
        end
    end
    
    % 应用方位过滤提高重建质量
    Z = apply_azimuth_filter(Z);
    
    X_sparse = Z;
    Z_sparse = Z;
end

% 复数软阈值函数
function z = apply_complex_soft_threshold(x, threshold)
    % 计算幅度和相位
    abs_x = abs(x);
    phase_x = angle(x);
    
    % 应用软阈值
    abs_z = max(abs_x - threshold, 0);
    
    % 重建复数
    z = abs_z .* exp(1j * phase_x);
end

% 方位滤波函数
function z = apply_azimuth_filter(x)
    % 简单的三点移动平均滤波
    N = length(x);
    z = x;
    
    for i = 2:N-1
        % 在频域中应用平滑处理
        if abs(x(i)) < 0.1 * max(abs(x))
            z(i) = 0.25 * x(i-1) + 0.5 * x(i) + 0.25 * x(i+1);
        end
    end
end 