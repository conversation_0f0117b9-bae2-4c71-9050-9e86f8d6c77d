% =========================================================================
% admm_reconstruct_improved.m
% 改进的基于ADMM的稀疏重建函数，增加形态学约束和自适应参数调整
% =========================================================================
function [X_sparse, Z_sparse, Lambda_dual_out] = admm_reconstruct_improved(Y_fft, params_admm, X_init, Z_init, Lambda_init)
    rho = params_admm.rho;
    lambda_s = params_admm.lambda_sparsity; 
    max_iter = params_admm.max_iter;
    tol = params_admm.tol;

    X = X_init;
    Z = Z_init;
    Lambda_dual = Lambda_init; % 对偶变量
    
    % 自适应调整ADMM参数
    rho_adaptive = rho;
    lambda_s_adaptive = lambda_s;
    mu = 10; % 惩罚参数更新系数
    tau_incr = 2;  % 增加系数
    tau_decr = 2;  % 减少系数
    
    % 初始残差值
    primal_residual_prev = Inf;
    dual_residual_prev = Inf;
    
    % 获取信号能量分布，用于调整稀疏正则化参数
    energy_profile = abs(Y_fft).^2;
    energy_profile = energy_profile / max(energy_profile + eps);
    
    % 估计噪声水平
    sorted_energy = sort(energy_profile);
    noise_level = mean(sorted_energy(1:floor(length(sorted_energy)/4)));
    
    % 根据信噪比调整稀疏性参数
    snr_est = max(energy_profile) / (noise_level + eps);
    if snr_est > 20
        lambda_s_adaptive = lambda_s_adaptive * 0.8; % 高信噪比，减小稀疏性约束
    elseif snr_est < 5
        lambda_s_adaptive = lambda_s_adaptive * 1.5; % 低信噪比，增加稀疏性约束
    end

    for iter = 1:max_iter
        Z_prev = Z; % 用于双残差计算
        
        % 更新X (数据保真项)
        % min_X 0.5*||X - Y_fft||^2 + <Lambda_dual, X-Z> + (rho/2)*||X-Z||_F^2
        X = (Y_fft - Lambda_dual + rho_adaptive * Z) / (1 + rho_adaptive);
        
        % 更新Z (稀疏项)
        % min_Z lambda_s*||Z||_1 + <Lambda_dual, X-Z> + (rho/2)*||Z-X-Lambda_dual/rho||_F^2
        Z_tilde = X + Lambda_dual/rho_adaptive;
        
        % 应用加权软阈值，考虑频率连续性
        Z = weighted_soft_threshold(Z_tilde, lambda_s_adaptive/rho_adaptive, energy_profile);
        
        % 应用形态学约束 - 保持相邻频率点的连续性
        Z = apply_morphological_constraint(Z);
        
        % 更新Lambda_dual (拉格朗日乘子 / 对偶变量)
        Lambda_dual = Lambda_dual + rho_adaptive * (X - Z);
        
        % 收敛检查 (基于原始残差和双残差)
        primal_residual = norm(X - Z, 'fro'); % Frobenius范数
        dual_residual = rho_adaptive * norm(Z - Z_prev, 'fro');
        
        % 设定收敛阈值
        eps_pri = tol * max(norm(X,'fro'), norm(Z,'fro'));
        eps_dual = tol * norm(Lambda_dual,'fro');

        % 自适应调整rho
        if primal_residual > mu * dual_residual
            rho_adaptive = rho_adaptive * tau_incr;
            Lambda_dual = Lambda_dual / tau_incr;
        elseif dual_residual > mu * primal_residual
            rho_adaptive = rho_adaptive / tau_decr;
            Lambda_dual = Lambda_dual * tau_decr;
        end
        
        % 检查收敛
        if primal_residual < eps_pri && dual_residual < eps_dual
            break;
        end
        
        % 检查振荡并调整lambda_s
        if iter > 1 && primal_residual > primal_residual_prev && dual_residual > dual_residual_prev
            lambda_s_adaptive = lambda_s_adaptive * 0.95; % 减小正则化参数
        end
        
        primal_residual_prev = primal_residual;
        dual_residual_prev = dual_residual;
    end
    
    % 最终清理 - 去除微小值
    X_sparse = X;
    Z_sparse = Z;
    Lambda_dual_out = Lambda_dual;
end

% 辅助函数: 加权软阈值
function y = weighted_soft_threshold(x, threshold_val, weights)
    % 将权重归一化到[0.5, 1]范围，保持阈值在合理范围内
    norm_weights = 0.5 + 0.5 * (weights / max(weights + eps));
    
    % 应用加权软阈值
    adjusted_threshold = threshold_val * (1 - norm_weights);
    y = sign(x) .* max(abs(x) - adjusted_threshold, 0);
end

% 辅助函数: 形态学约束 - 保持频谱的连续性
function z_out = apply_morphological_constraint(z_in)
    z_out = z_in;
    N = length(z_in);
    
    % 对频谱进行平滑处理
    abs_z = abs(z_in);
    
    % 检测孤立的大值点
    for i = 2:N-1
        % 如果当前点比相邻点大很多，可能是异常点
        if abs_z(i) > 3*abs_z(i-1) && abs_z(i) > 3*abs_z(i+1)
            % 使用相邻点的平均值替代
            z_out(i) = (z_in(i-1) + z_in(i+1))/2;
        end
    end
    
    % 平滑处理 - 小窗口均值滤波，保持相位
    window_size = 3;
    half_window = floor(window_size/2);
    
    for i = 1:N
        % 获取窗口内的点
        idx_start = max(1, i-half_window);
        idx_end = min(N, i+half_window);
        window_indices = idx_start:idx_end;
        
        % 计算幅度加权平均
        weights = abs_z(window_indices);
        weights = weights / sum(weights + eps);
        
        % 保持相位，仅平滑幅度
        if sum(weights) > 0
            avg_amp = sum(abs_z(window_indices) .* weights);
            % 仅当当前幅度小于平均值的一定比例时才平滑
            if abs_z(i) < 0.3*avg_amp
                z_out(i) = avg_amp * exp(1j * angle(z_in(i)));
            end
        end
    end
end 