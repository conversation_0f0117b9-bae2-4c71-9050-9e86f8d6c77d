% =========================================================================
% autofocus_correction.m
% 自动聚焦算法，通过图像熵最小化优化相位补偿
% =========================================================================
function s_corrected = autofocus_correction(s_compensated, ISAR_image)
    % 获取数据尺寸
    [num_range_bins, num_azimuth] = size(s_compensated);
    
    % 初始化输出
    s_corrected = s_compensated;
    
    % 计算归一化慢时间轴
    tm_normalized = (0:num_azimuth-1) / num_azimuth;
    
    % 为自动聚焦选择高信噪比距离单元
    % 计算每个距离单元的能量
    range_energy = sum(abs(ISAR_image).^2, 2);
    
    % 按能量排序
    [~, sorted_indices] = sort(range_energy, 'descend');
    
    % 选择前30%的高能量距离单元
    num_selected = max(round(0.3 * num_range_bins), 3);
    selected_ranges = sorted_indices(1:num_selected);
    
    % 初始化全局相位误差估计
    phase_error_global = zeros(1, num_azimuth);
    
    % 对每个选定的距离单元执行最小熵聚焦
    for idx = 1:length(selected_ranges)
        r_idx = selected_ranges(idx);
        
        % 获取当前距离单元信号
        signal = s_compensated(r_idx, :);
        
        % 跳过能量过低的距离单元
        if sum(abs(signal).^2) < 1e-8
            continue;
        end
        
        % 执行最小熵相位估计
        phase_error = min_entropy_phase_estimation(signal, tm_normalized);
        
        % 累积全局相位误差
        phase_error_global = phase_error_global + phase_error;
    end
    
    % 计算平均相位误差
    if length(selected_ranges) > 0
        phase_error_global = phase_error_global / length(selected_ranges);
    end
    
    % 应用平滑处理以减少噪声影响
    phase_error_global = smooth_phase(phase_error_global);
    
    % 应用相位校正
    for r_idx = 1:num_range_bins
        s_corrected(r_idx, :) = s_compensated(r_idx, :) .* exp(-1j * phase_error_global);
    end
end

% 最小熵相位估计函数
function phase_error = min_entropy_phase_estimation(signal, tm_normalized)
    % 信号长度
    N = length(signal);
    
    % 初始化相位误差
    phase_error = zeros(1, N);
    
    % 使用多项式相位模型
    poly_order = 4;
    
    % 定义搜索范围和步长
    phase_ranges = [-pi/4, pi/4; -pi/8, pi/8; -pi/16, pi/16; -pi/32, pi/32];
    phase_steps = [pi/20, pi/40, pi/80, pi/160];
    
    % 初始化多项式系数
    poly_coeffs = zeros(1, poly_order);
    
    % 为每个阶次执行多项式系数搜索
    for order = 1:poly_order
        % 从之前的系数计算当前的相位补偿
        current_phase = zeros(1, N);
        for i = 1:order-1
            if i == 1
                current_phase = current_phase + poly_coeffs(i) * tm_normalized;
            elseif i == 2
                current_phase = current_phase + poly_coeffs(i) * tm_normalized.^2;
            elseif i == 3
                current_phase = current_phase + poly_coeffs(i) * tm_normalized.^3;
            elseif i == 4
                current_phase = current_phase + poly_coeffs(i) * tm_normalized.^4;
            end
        end
        
        % 去除当前估计的相位
        signal_demod = signal .* exp(-1j * current_phase);
        
        % 定义搜索范围
        range_min = phase_ranges(min(order, size(phase_ranges, 1)), 1);
        range_max = phase_ranges(min(order, size(phase_ranges, 1)), 2);
        step = phase_steps(min(order, length(phase_steps)));
        
        coefficient_values = range_min:step:range_max;
        
        % 初始化最小熵和最佳系数
        min_entropy = Inf;
        best_coeff = 0;
        
        % 网格搜索最佳系数
        for coeff_idx = 1:length(coefficient_values)
            coeff = coefficient_values(coeff_idx);
            
            % 构建相位校正
            if order == 1
                test_phase = coeff * tm_normalized;
            elseif order == 2
                test_phase = coeff * tm_normalized.^2;
            elseif order == 3
                test_phase = coeff * tm_normalized.^3;
            elseif order == 4
                test_phase = coeff * tm_normalized.^4;
            end
            
            % 应用相位校正
            test_signal = signal_demod .* exp(-1j * test_phase);
            
            % 计算频谱
            test_spectrum = abs(fft(test_signal)).^2;
            test_spectrum = test_spectrum / sum(test_spectrum + eps);
            
            % 计算熵
            entropy_value = -sum(test_spectrum .* log2(test_spectrum + eps));
            
            % 更新最佳系数
            if entropy_value < min_entropy
                min_entropy = entropy_value;
                best_coeff = coeff;
            end
        end
        
        % 保存最佳系数
        poly_coeffs(order) = best_coeff;
    end
    
    % 构建最终的相位误差
    for order = 1:poly_order
        if order == 1
            phase_error = phase_error + poly_coeffs(order) * tm_normalized;
        elseif order == 2
            phase_error = phase_error + poly_coeffs(order) * tm_normalized.^2;
        elseif order == 3
            phase_error = phase_error + poly_coeffs(order) * tm_normalized.^3;
        elseif order == 4
            phase_error = phase_error + poly_coeffs(order) * tm_normalized.^4;
        end
    end
end

% 平滑相位函数
function smoothed_phase = smooth_phase(phase)
    % 使用移动平均滤波
    window_size = 5;
    N = length(phase);
    smoothed_phase = phase;
    
    % 确保相位连续性
    unwrapped_phase = unwrap(phase);
    
    % 应用移动平均
    for i = 1:N
        % 确定窗口范围
        start_idx = max(1, i - floor(window_size/2));
        end_idx = min(N, i + floor(window_size/2));
        
        % 计算平均
        smoothed_phase(i) = mean(unwrapped_phase(start_idx:end_idx));
    end
    
    % 使用多项式拟合进一步平滑
    poly_order = min(6, floor(N/10));
    t = (0:N-1) / (N-1);
    p = polyfit(t, smoothed_phase, poly_order);
    smoothed_phase = polyval(p, t);
end 