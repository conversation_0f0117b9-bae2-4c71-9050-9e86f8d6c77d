% =========================================================================
% vmd_decompose_improved.m
% 改进的变分模态分解函数，用于ISAR信号的复杂模态分离
% =========================================================================
function [u, omega_modes, lambda_dual_out] = vmd_decompose_improved(signal, params_vmd, u_init, omega_init, guidance_phi_k, alpha_guidance)
    alpha = params_vmd.alpha; 
    tau = params_vmd.tau;     
    K = params_vmd.K;         
    tol = params_vmd.tol;     
    max_iter = params_vmd.max_iter;

    if nargin < 5 || isempty(guidance_phi_k)
        alpha_guidance = 0;
    end

    N = length(signal);
    signal_fft = fft(signal);
    f_axis_normalized = (0:N-1)/N; 

    u = u_init;
    omega_modes = omega_init;
    u_fft = zeros(K, N, 'like', 1j*signal(1));
    for k_idx_init_u = 1:K
        u_fft(k_idx_init_u,:) = fft(u(k_idx_init_u,:));
    end
    
    lambda_dual = zeros(1, N, 'like', 1j*signal(1)); % 时域拉格朗日乘子 lambda_hat
    
    % 动态调整alpha以提高复杂信号的分解能力
    alpha_adjusted = alpha * ones(1, K);
    
    % 记录上一次迭代的模态信息，用于动态调整
    prev_omega = omega_modes;
    converged_modes = false(K, 1);

    for iter = 1:max_iter
        u_sum_fft_prev_iter = sum(u_fft,1); % 用于收敛检查
        
        for k_idx = 1:K
            % 如果该模态已经收敛，则跳过进一步优化
            if converged_modes(k_idx)
                continue;
            end
            
            sum_other_modes_fft = sum(u_fft, 1) - u_fft(k_idx,:);
            
            % 更新 u_k (频域)
            % 分子: signal_fft - sum_other_modes_fft + fft(lambda_dual)/2
            numerator_fft = signal_fft - sum_other_modes_fft + fft(lambda_dual)/2;
            
            % 分母: 1 + 2*alpha*(omega_normalized - omega_k)^2
            denominator = 1 + 2*alpha_adjusted(k_idx)*(f_axis_normalized - omega_modes(k_idx)).^2;
            
            if alpha_guidance > 0 && size(guidance_phi_k,1) >= k_idx && any(guidance_phi_k(k_idx,:))
                % 构造相位先验的频谱
                phase_prior_term_fft = fft(exp(1j * guidance_phi_k(k_idx,:))); 
                
                % 加入引导项
                numerator_fft = numerator_fft + alpha_guidance * phase_prior_term_fft;
                denominator = denominator + alpha_guidance;
            end
            
            u_fft(k_idx,:) = numerator_fft ./ denominator;
            u(k_idx,:) = ifft(u_fft(k_idx,:)); % 更新时域模态
            
            % 使用平滑方法更新中心频率
            power_spectrum_uk = abs(u_fft(k_idx,:)).^2;
            
            % 平滑功率谱以减少噪声影响
            power_spectrum_smooth = smooth_spectrum(power_spectrum_uk, 5);
            
            if sum(power_spectrum_smooth) > 1e-12 % 避免除零
                omega_modes(k_idx) = sum(f_axis_normalized .* power_spectrum_smooth) / sum(power_spectrum_smooth);
            end
            
            % 检查该模态是否已收敛
            if iter > 1
                omega_change = abs(omega_modes(k_idx) - prev_omega(k_idx));
                if omega_change < tol/10
                    converged_modes(k_idx) = true;
                else
                    % 动态调整alpha - 如果频率变化剧烈，增加约束
                    if omega_change > 0.01
                        alpha_adjusted(k_idx) = alpha_adjusted(k_idx) * 1.1; % 增加约束强度
                    else
                        alpha_adjusted(k_idx) = max(alpha_adjusted(k_idx) * 0.95, alpha/2); % 减小约束，但不低于初始值的一半
                    end
                end
            end
        end
        
        % 更新上一次迭代的中心频率
        prev_omega = omega_modes;
        
        % 更新拉格朗日乘子 lambda_dual (时域)
        if tau > 0 
            current_sum_u_time = sum(u, 1);
            lambda_dual = lambda_dual + tau * (signal - current_sum_u_time);
        end
        
        % 收敛检查
        if iter > 1
            u_sum_change = norm(sum(u_fft,1) - u_sum_fft_prev_iter_loopstart) / (norm(u_sum_fft_prev_iter_loopstart) + eps);
            if u_sum_change < tol || all(converged_modes)
                break;
            end
        end
        u_sum_fft_prev_iter_loopstart = sum(u_fft,1); 
    end
    
    % 分离模态后的后处理 - 合并相似频率的模态
    u = merge_similar_modes(u, omega_modes, tol);
    
    lambda_dual_out = lambda_dual; % 输出最终的lambda
end

% 辅助函数：平滑频谱
function spectrum_smooth = smooth_spectrum(spectrum, window_size)
    N = length(spectrum);
    spectrum_smooth = spectrum;
    half_window = floor(window_size/2);
    
    for i = 1:N
        window_start = max(1, i-half_window);
        window_end = min(N, i+half_window);
        spectrum_smooth(i) = mean(spectrum(window_start:window_end));
    end
end

% 辅助函数：合并相似频率的模态
function u_merged = merge_similar_modes(u, omega_modes, tol)
    [K, N] = size(u);
    u_merged = u;
    
    % 对相近的模态进行合并
    processed = false(1, K);
    for i = 1:K
        if processed(i)
            continue;
        end
        
        similar_modes = false(1, K);
        similar_modes(i) = true;
        
        for j = i+1:K
            if abs(omega_modes(i) - omega_modes(j)) < tol*10 % 频率相近的阈值可调整
                similar_modes(j) = true;
                processed(j) = true;
            end
        end
        
        % 如果找到相似模态，则合并
        if sum(similar_modes) > 1
            merged_mode = zeros(1, N, 'like', u(1,1));
            for k = 1:K
                if similar_modes(k)
                    merged_mode = merged_mode + u(k,:);
                    u_merged(k,:) = 0; % 清除原模态
                end
            end
            
            % 将合并后的模态放回第一个位置
            idx = find(similar_modes, 1);
            u_merged(idx,:) = merged_mode;
        end
    end
end 